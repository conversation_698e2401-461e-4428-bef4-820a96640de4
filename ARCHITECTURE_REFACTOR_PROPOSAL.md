# 🏗️ Clean Architecture Refactor Proposal

## 📊 Current Structure Analysis

### ❌ **Issues Identified:**

1. **Duplicate Directories**
   - `internal/delivery/http` vs `internal/interfaces/http` (both empty/unused)
   - `internal/config` (empty) vs `internal/infrastructure/config`

2. **Poor Domain Separation**
   - All entities in single `entities/` folder (19 files)
   - Usecases not grouped by domain (20+ files in root)
   - Repositories mixed without clear domain boundaries

3. **Inconsistent Layering**
   - Domain services mixed with infrastructure concerns
   - Storage interfaces in domain but implementations in infrastructure
   - No clear application layer separation

4. **Missing Standard Patterns**
   - No ports/adapters pattern
   - No application services layer
   - No clear dependency injection structure

## 🎯 **Proposed Clean Architecture Structure**

```
cmd/
├── api/                    # API server entry point
├── worker/                 # Background workers (future)
└── migrate/                # Database migration tool

internal/
├── app/                    # Application Layer
│   ├── commands/           # Command handlers (CQRS)
│   ├── queries/            # Query handlers (CQRS)
│   └── services/           # Application services
│
├── domain/                 # Domain Layer (Business Logic)
│   ├── user/               # User Domain
│   │   ├── entities/       # User entities
│   │   ├── services/       # User domain services
│   │   ├── repositories/   # User repository interfaces
│   │   └── errors/         # User-specific errors
│   ├── product/            # Product Domain
│   │   ├── entities/       # Product, Category, Inventory
│   │   ├── services/       # Pricing, Stock services
│   │   ├── repositories/   # Product repository interfaces
│   │   └── errors/         # Product-specific errors
│   ├── order/              # Order Domain
│   │   ├── entities/       # Order, OrderItem, Payment
│   │   ├── services/       # Order processing, State machine
│   │   ├── repositories/   # Order repository interfaces
│   │   └── errors/         # Order-specific errors
│   ├── cart/               # Cart Domain
│   ├── review/             # Review Domain
│   ├── coupon/             # Coupon Domain
│   └── shared/             # Shared domain concepts
│       ├── valueobjects/   # Money, Address, etc.
│       └── events/         # Domain events
│
├── ports/                  # Ports (Interfaces)
│   ├── input/              # Input ports (use cases)
│   │   ├── user/           # User use cases
│   │   ├── product/        # Product use cases
│   │   └── order/          # Order use cases
│   └── output/             # Output ports (repositories, services)
│       ├── repositories/   # Repository interfaces
│       ├── services/       # External service interfaces
│       └── events/         # Event publisher interfaces
│
├── adapters/               # Adapters (Infrastructure)
│   ├── input/              # Input adapters
│   │   ├── http/           # HTTP handlers
│   │   │   ├── user/       # User endpoints
│   │   │   ├── product/    # Product endpoints
│   │   │   ├── order/      # Order endpoints
│   │   │   └── middleware/ # HTTP middleware
│   │   └── grpc/           # gRPC handlers (future)
│   ├── output/             # Output adapters
│   │   ├── persistence/    # Database implementations
│   │   │   ├── postgres/   # PostgreSQL repositories
│   │   │   └── redis/      # Redis cache
│   │   ├── messaging/      # Message queue adapters
│   │   ├── payment/        # Payment service adapters
│   │   ├── storage/        # File storage adapters
│   │   └── notification/   # Notification adapters
│   └── config/             # Configuration
│
└── shared/                 # Shared Infrastructure
    ├── logger/             # Logging utilities
    ├── validator/          # Validation utilities
    ├── middleware/         # Shared middleware
    └── utils/              # Common utilities

pkg/                        # Public packages
├── errors/                 # Error handling
├── logger/                 # Logging package
├── validator/              # Validation package
└── utils/                  # Utility functions
```

## 🔄 **Migration Strategy**

### Phase 1: Domain Separation
1. **Group entities by domain**
   ```
   internal/domain/user/entities/
   internal/domain/product/entities/
   internal/domain/order/entities/
   ```

2. **Move domain services**
   ```
   internal/domain/user/services/
   internal/domain/order/services/
   internal/domain/payment/services/
   ```

### Phase 2: Use Cases Restructuring
1. **Group use cases by domain**
   ```
   internal/ports/input/user/
   internal/ports/input/product/
   internal/ports/input/order/
   ```

2. **Create application services**
   ```
   internal/app/services/user_service.go
   internal/app/services/product_service.go
   ```

### Phase 3: Infrastructure Cleanup
1. **Consolidate adapters**
   ```
   internal/adapters/input/http/
   internal/adapters/output/persistence/
   ```

2. **Remove duplicates**
   - Remove `internal/interfaces/`
   - Consolidate config directories
   - Clean up empty directories

## 🎯 **Benefits of New Structure**

### 1. **Clear Domain Boundaries**
- Each domain has its own entities, services, repositories
- Easier to understand business logic
- Better team collaboration (domain experts)

### 2. **Dependency Rule Compliance**
- Clear inward dependencies only
- Domain layer completely isolated
- Infrastructure depends on domain, not vice versa

### 3. **Testability**
- Easy to mock interfaces
- Domain logic testable in isolation
- Clear test boundaries

### 4. **Scalability**
- Easy to extract domains to microservices
- Clear API boundaries
- Independent domain evolution

### 5. **Maintainability**
- Easier to find related code
- Clear responsibility separation
- Reduced cognitive load

## 📋 **Implementation Checklist**

### Domain Layer
- [ ] Create domain directories (user, product, order, etc.)
- [ ] Move entities to respective domains
- [ ] Move domain services to respective domains
- [ ] Create domain-specific error types
- [ ] Define repository interfaces in domains

### Application Layer
- [ ] Create application services
- [ ] Implement command/query handlers
- [ ] Define use case interfaces
- [ ] Create application-level DTOs

### Infrastructure Layer
- [ ] Reorganize HTTP handlers by domain
- [ ] Group repository implementations
- [ ] Consolidate configuration
- [ ] Clean up duplicate directories

### Shared Components
- [ ] Move common utilities to pkg/
- [ ] Create shared value objects
- [ ] Define domain events
- [ ] Implement event handling

## 🚀 **Next Steps**

1. **Start with Domain Separation** - Move entities to domain folders
2. **Refactor Use Cases** - Group by domain and create clear interfaces
3. **Clean Infrastructure** - Remove duplicates and organize adapters
4. **Add Missing Layers** - Implement application services and ports
5. **Update Dependencies** - Fix all import paths
6. **Update Tests** - Ensure all tests still pass
7. **Update Documentation** - Reflect new structure

This refactoring will make the codebase much more maintainable and truly follow Clean Architecture principles!
