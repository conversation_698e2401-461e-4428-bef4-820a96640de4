package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	// Infrastructure
	"ecom-golang-clean-architecture/internal/infrastructure/config"
	"ecom-golang-clean-architecture/internal/infrastructure/database"
	
	// Adapters
	httpAdapter "ecom-golang-clean-architecture/internal/adapters/input/http"
	postgresAdapter "ecom-golang-clean-architecture/internal/adapters/output/persistence/postgres"
	
	// Application Services
	"ecom-golang-clean-architecture/internal/app/services"
	
	// Domain Repositories
	userRepo "ecom-golang-clean-architecture/internal/domain/user/repositories"
	productRepo "ecom-golang-clean-architecture/internal/domain/product/repositories"
	
	// Ports
	userPorts "ecom-golang-clean-architecture/internal/ports/input/user"
	productPorts "ecom-golang-clean-architecture/internal/ports/input/product"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// Application holds all dependencies
type Application struct {
	config *config.Config
	db     *gorm.DB
	
	// Repositories
	userRepository     userRepo.UserRepository
	addressRepository  userRepo.AddressRepository
	productRepository  productRepo.ProductRepository
	categoryRepository productRepo.CategoryRepository
	
	// Services
	userService     userPorts.UserService
	addressService  userPorts.AddressService
	productService  productPorts.ProductService
	categoryService productPorts.CategoryService
	
	// HTTP Server
	httpServer *http.Server
}

// @title E-commerce API - Clean Architecture
// @version 2.0
// @description A modern e-commerce API built with Go and Clean Architecture principles
// @termsOfService http://swagger.io/terms/

// @contact.name API Support
// @contact.url http://www.swagger.io/support
// @contact.email <EMAIL>

// @license.name MIT
// @license.url https://opensource.org/licenses/MIT

// @host localhost:8080
// @BasePath /api/v1

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

func main() {
	app, err := NewApplication()
	if err != nil {
		log.Fatal("Failed to initialize application:", err)
	}

	if err := app.Start(); err != nil {
		log.Fatal("Failed to start application:", err)
	}
}

// NewApplication creates and configures the application
func NewApplication() (*Application, error) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		return nil, err
	}

	// Set Gin mode
	if cfg.App.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	app := &Application{
		config: cfg,
	}

	// Initialize dependencies
	if err := app.initializeDatabase(); err != nil {
		return nil, err
	}

	if err := app.initializeRepositories(); err != nil {
		return nil, err
	}

	if err := app.initializeServices(); err != nil {
		return nil, err
	}

	if err := app.initializeHTTPServer(); err != nil {
		return nil, err
	}

	return app, nil
}

// initializeDatabase sets up database connection and runs migrations
func (app *Application) initializeDatabase() error {
	log.Println("Initializing database connection...")
	
	db, err := database.Connect(app.config)
	if err != nil {
		return err
	}
	
	app.db = db
	
	// Run migrations
	if err := database.AutoMigrate(db); err != nil {
		return err
	}
	
	log.Println("Database initialized successfully")
	return nil
}

// initializeRepositories creates repository implementations
func (app *Application) initializeRepositories() error {
	log.Println("Initializing repositories...")
	
	// User domain repositories
	app.userRepository = postgresAdapter.NewUserRepository(app.db)
	app.addressRepository = postgresAdapter.NewAddressRepository(app.db)
	
	// Product domain repositories
	app.productRepository = postgresAdapter.NewProductRepository(app.db)
	app.categoryRepository = postgresAdapter.NewCategoryRepository(app.db)
	
	log.Println("Repositories initialized successfully")
	return nil
}

// initializeServices creates application services with dependency injection
func (app *Application) initializeServices() error {
	log.Println("Initializing application services...")
	
	// Initialize external services
	jwtService := services.NewJWTService(app.config.JWT.Secret, app.config.JWT.ExpireHours)
	emailService := services.NewEmailService(app.config.Email)
	
	// Initialize application services
	app.userService = services.NewUserApplicationService(
		app.userRepository,
		app.addressRepository,
		jwtService,
		emailService,
	)
	
	app.addressService = services.NewAddressApplicationService(
		app.addressRepository,
	)
	
	app.productService = services.NewProductApplicationService(
		app.productRepository,
		app.categoryRepository,
	)
	
	app.categoryService = services.NewCategoryApplicationService(
		app.categoryRepository,
	)
	
	log.Println("Application services initialized successfully")
	return nil
}

// initializeHTTPServer creates and configures the HTTP server
func (app *Application) initializeHTTPServer() error {
	log.Println("Initializing HTTP server...")
	
	// Create router with clean architecture
	router := httpAdapter.NewRouter(httpAdapter.RouterConfig{
		UserService:     app.userService,
		AddressService:  app.addressService,
		ProductService:  app.productService,
		CategoryService: app.categoryService,
		JWTSecret:       app.config.JWT.Secret,
	})
	
	// Create HTTP server
	app.httpServer = &http.Server{
		Addr:         app.config.Server.GetAddress(),
		Handler:      router,
		ReadTimeout:  15 * time.Second,
		WriteTimeout: 15 * time.Second,
		IdleTimeout:  60 * time.Second,
	}
	
	log.Println("HTTP server initialized successfully")
	return nil
}

// Start starts the application
func (app *Application) Start() error {
	log.Printf("Starting server on %s", app.config.Server.GetAddress())
	
	// Start server in a goroutine
	go func() {
		if err := app.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()
	
	log.Printf("Server started successfully on %s", app.config.Server.GetAddress())
	log.Println("Press Ctrl+C to shutdown")
	
	// Wait for interrupt signal to gracefully shutdown
	app.waitForShutdown()
	
	return nil
}

// waitForShutdown waits for interrupt signal and gracefully shuts down the server
func (app *Application) waitForShutdown() {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit
	
	log.Println("Shutting down server...")
	
	// Create a deadline for shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	// Shutdown HTTP server
	if err := app.httpServer.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}
	
	// Close database connection
	if app.db != nil {
		sqlDB, err := app.db.DB()
		if err == nil {
			sqlDB.Close()
		}
	}
	
	log.Println("Server exited")
}

// Health check for the application
func (app *Application) HealthCheck() error {
	// Check database connection
	sqlDB, err := app.db.DB()
	if err != nil {
		return err
	}
	
	if err := sqlDB.Ping(); err != nil {
		return err
	}
	
	return nil
}
