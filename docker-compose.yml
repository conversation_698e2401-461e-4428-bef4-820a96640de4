version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ecom_postgres
    environment:
      POSTGRES_DB: ecommerce_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - ecom_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d ecommerce_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ecom_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecom_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # E-commerce API
  api:
    build: .
    container_name: ecom_api
    ports:
      - "8080:8080"
    environment:
      # App Configuration
      - APP_ENV=development
      - APP_HOST=0.0.0.0
      - APP_PORT=8080
      - APP_DEBUG=true

      # Database Configuration
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=ecommerce_db
      - DB_SSLMODE=disable

      # Redis Configuration
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - REDIS_DB=0

      # JWT Configuration
      - JWT_SECRET=ecom-secure-jwt-secret-key-change-in-production-2024
      - JWT_EXPIRE_HOURS=24

      # CORS Configuration
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8080
      - CORS_ALLOWED_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
      - CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
      - CORS_ALLOW_CREDENTIALS=true
      - CORS_MAX_AGE=86400

      # Payment Configuration
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}

      # OAuth Configuration
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - GOOGLE_REDIRECT_URL=http://localhost:8080/api/v1/auth/google/callback
      - FACEBOOK_CLIENT_ID=${FACEBOOK_CLIENT_ID}
      - FACEBOOK_CLIENT_SECRET=${FACEBOOK_CLIENT_SECRET}
      - FACEBOOK_REDIRECT_URL=http://localhost:8080/api/v1/auth/facebook/callback

      # Email Configuration
      - EMAIL_SMTP_HOST=mailhog
      - EMAIL_SMTP_PORT=1025
      - EMAIL_SMTP_USERNAME=
      - EMAIL_SMTP_PASSWORD=
      - EMAIL_FROM=<EMAIL>

      # Upload Configuration
      - UPLOAD_PATH=/app/uploads
      - UPLOAD_MAX_SIZE=10485760

      # Logging Configuration
      - LOG_LEVEL=info
      - LOG_FORMAT=json
      - FACEBOOK_APP_ID=${FACEBOOK_APP_ID}
      - FACEBOOK_APP_SECRET=${FACEBOOK_APP_SECRET}
      - FACEBOOK_REDIRECT_URL=http://localhost:8080/api/v1/auth/facebook/callback
      - CORS_ALLOWED_METHODS=GET,POST,PUT,PATCH,DELETE,OPTIONS
    # volumes:
      # - ./uploads:/app/uploads
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecom_network
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: ecom_mailhog
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # Web UI
    networks:
      - ecom_network
    restart: unless-stopped

  # pgAdmin (optional - for database management)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ecom_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - postgres
    networks:
      - ecom_network
    profiles:
      - tools

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  ecom_network:
    driver: bridge
