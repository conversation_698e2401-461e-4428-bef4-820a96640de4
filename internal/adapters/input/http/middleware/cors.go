package middleware

import (
	"net/http"
	"strings"

	"ecom-golang-clean-architecture/internal/infrastructure/config"
	"github.com/gin-gonic/gin"
)

// CORSMiddleware creates CORS middleware
func CORSMiddleware(cfg *config.CORSConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// Check if origin is allowed
		if isOriginAllowed(origin, cfg.AllowedOrigins) {
			c.<PERSON>("Access-Control-Allow-Origin", origin)
		}

		c.<PERSON><PERSON>("Access-Control-Allow-Methods", strings.Join(cfg.AllowedMethods, ", "))
		c.<PERSON>("Access-Control-Allow-Headers", strings.Join(cfg.AllowedHeaders, ", "))
		c.<PERSON>("Access-Control-Allow-Credentials", "true")
		c.<PERSON>("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	}
}

// isOriginAllowed checks if the origin is in the allowed list
func isOriginAllowed(origin string, allowedOrigins []string) bool {
	for _, allowed := range allowedOrigins {
		if allowed == "*" || allowed == origin {
			return true
		}
	}
	return false
}
