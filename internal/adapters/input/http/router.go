package http

import (
	"ecom-golang-clean-architecture/internal/adapters/input/http/middleware"
	userHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/user"
	productHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/product"
	orderHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/order"
	cartHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/cart"
	reviewHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/review"
	couponHandlers "ecom-golang-clean-architecture/internal/adapters/input/http/coupon"
	
	userPorts "ecom-golang-clean-architecture/internal/ports/input/user"
	productPorts "ecom-golang-clean-architecture/internal/ports/input/product"
	
	"github.com/gin-gonic/gin"
)

// RouterConfig holds the configuration for HTTP router
type RouterConfig struct {
	// Services
	UserService    userPorts.UserService
	AddressService userPorts.AddressService
	ProductService productPorts.ProductService
	CategoryService productPorts.CategoryService
	
	// Middleware dependencies
	JWTSecret string
}

// NewRouter creates a new HTTP router with clean architecture
func NewRouter(config RouterConfig) *gin.Engine {
	router := gin.New()
	
	// Global middleware
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.SecurityMiddleware())
	router.Use(middleware.LoggingMiddleware())
	
	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"service": "ecom-api",
		})
	})
	
	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Public routes
		setupPublicRoutes(v1, config)
		
		// Protected routes
		protected := v1.Group("")
		protected.Use(middleware.AuthMiddleware(config.JWTSecret))
		setupProtectedRoutes(protected, config)
		
		// Admin routes
		admin := v1.Group("/admin")
		admin.Use(middleware.AuthMiddleware(config.JWTSecret))
		admin.Use(middleware.AdminMiddleware())
		setupAdminRoutes(admin, config)
	}
	
	return router
}

// setupPublicRoutes configures public API routes
func setupPublicRoutes(rg *gin.RouterGroup, config RouterConfig) {
	// User authentication routes
	auth := rg.Group("/auth")
	{
		userHandler := userHandlers.NewUserHandler(config.UserService)
		auth.POST("/register", userHandler.Register)
		auth.POST("/login", userHandler.Login)
		auth.POST("/refresh", userHandler.RefreshToken)
		auth.POST("/forgot-password", userHandler.ForgotPassword)
		auth.POST("/reset-password", userHandler.ResetPassword)
		auth.GET("/verify-email/:token", userHandler.VerifyEmail)
	}
	
	// Public product routes
	products := rg.Group("/products")
	{
		productHandler := productHandlers.NewProductHandler(config.ProductService)
		products.GET("", productHandler.ListProducts)
		products.GET("/:id", productHandler.GetProduct)
		products.GET("/sku/:sku", productHandler.GetProductBySKU)
		products.GET("/search", productHandler.SearchProducts)
		products.GET("/featured", productHandler.GetFeaturedProducts)
		products.GET("/on-sale", productHandler.GetProductsOnSale)
	}
	
	// Public category routes
	categories := rg.Group("/categories")
	{
		categoryHandler := productHandlers.NewCategoryHandler(config.CategoryService)
		categories.GET("", categoryHandler.ListCategories)
		categories.GET("/:id", categoryHandler.GetCategory)
		categories.GET("/slug/:slug", categoryHandler.GetCategoryBySlug)
		categories.GET("/tree", categoryHandler.GetCategoryTree)
		categories.GET("/:id/products", categoryHandler.GetCategoryProducts)
	}
}

// setupProtectedRoutes configures protected API routes (requires authentication)
func setupProtectedRoutes(rg *gin.RouterGroup, config RouterConfig) {
	// User profile routes
	users := rg.Group("/users")
	{
		userHandler := userHandlers.NewUserHandler(config.UserService)
		users.GET("/profile", userHandler.GetProfile)
		users.PUT("/profile", userHandler.UpdateProfile)
		users.POST("/change-password", userHandler.ChangePassword)
		users.POST("/logout", userHandler.Logout)
		users.POST("/resend-verification", userHandler.ResendVerification)
	}
	
	// Address management routes
	addresses := rg.Group("/addresses")
	{
		addressHandler := userHandlers.NewAddressHandler(config.AddressService)
		addresses.GET("", addressHandler.GetAddresses)
		addresses.POST("", addressHandler.CreateAddress)
		addresses.GET("/:id", addressHandler.GetAddress)
		addresses.PUT("/:id", addressHandler.UpdateAddress)
		addresses.DELETE("/:id", addressHandler.DeleteAddress)
		addresses.POST("/:id/set-default", addressHandler.SetDefaultAddress)
	}
	
	// Cart routes
	cart := rg.Group("/cart")
	{
		// cartHandler := cartHandlers.NewCartHandler(config.CartService)
		// cart.GET("", cartHandler.GetCart)
		// cart.POST("/items", cartHandler.AddItem)
		// cart.PUT("/items/:id", cartHandler.UpdateItem)
		// cart.DELETE("/items/:id", cartHandler.RemoveItem)
		// cart.DELETE("", cartHandler.ClearCart)
	}
	
	// Order routes
	orders := rg.Group("/orders")
	{
		// orderHandler := orderHandlers.NewOrderHandler(config.OrderService)
		// orders.GET("", orderHandler.GetOrders)
		// orders.POST("", orderHandler.CreateOrder)
		// orders.GET("/:id", orderHandler.GetOrder)
		// orders.POST("/:id/cancel", orderHandler.CancelOrder)
	}
	
	// Review routes
	reviews := rg.Group("/reviews")
	{
		// reviewHandler := reviewHandlers.NewReviewHandler(config.ReviewService)
		// reviews.POST("", reviewHandler.CreateReview)
		// reviews.GET("/product/:productId", reviewHandler.GetProductReviews)
		// reviews.PUT("/:id", reviewHandler.UpdateReview)
		// reviews.DELETE("/:id", reviewHandler.DeleteReview)
		// reviews.POST("/:id/vote", reviewHandler.VoteReview)
	}
	
	// Wishlist routes
	wishlist := rg.Group("/wishlist")
	{
		// wishlistHandler := userHandlers.NewWishlistHandler(config.WishlistService)
		// wishlist.GET("", wishlistHandler.GetWishlist)
		// wishlist.POST("/items", wishlistHandler.AddItem)
		// wishlist.DELETE("/items/:id", wishlistHandler.RemoveItem)
	}
}

// setupAdminRoutes configures admin API routes
func setupAdminRoutes(rg *gin.RouterGroup, config RouterConfig) {
	// Admin user management
	users := rg.Group("/users")
	{
		userHandler := userHandlers.NewUserHandler(config.UserService)
		users.GET("", userHandler.GetUsers)
		users.GET("/:id", userHandler.GetUserByID)
		users.PUT("/:id/role", userHandler.UpdateUserRole)
		users.PUT("/:id/status", userHandler.UpdateUserStatus)
		users.DELETE("/:id", userHandler.DeleteUser)
	}
	
	// Admin product management
	products := rg.Group("/products")
	{
		productHandler := productHandlers.NewProductHandler(config.ProductService)
		products.POST("", productHandler.CreateProduct)
		products.PUT("/:id", productHandler.UpdateProduct)
		products.DELETE("/:id", productHandler.DeleteProduct)
		products.PUT("/:id/stock", productHandler.UpdateStock)
		products.PUT("/:id/price", productHandler.UpdatePrice)
		products.GET("/low-stock", productHandler.GetLowStockProducts)
	}
	
	// Admin category management
	categories := rg.Group("/categories")
	{
		categoryHandler := productHandlers.NewCategoryHandler(config.CategoryService)
		categories.POST("", categoryHandler.CreateCategory)
		categories.PUT("/:id", categoryHandler.UpdateCategory)
		categories.DELETE("/:id", categoryHandler.DeleteCategory)
	}
	
	// Admin order management
	orders := rg.Group("/orders")
	{
		// orderHandler := orderHandlers.NewOrderHandler(config.OrderService)
		// orders.GET("", orderHandler.GetAllOrders)
		// orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
		// orders.POST("/:id/refund", orderHandler.RefundOrder)
	}
	
	// Admin coupon management
	coupons := rg.Group("/coupons")
	{
		// couponHandler := couponHandlers.NewCouponHandler(config.CouponService)
		// coupons.GET("", couponHandler.GetCoupons)
		// coupons.POST("", couponHandler.CreateCoupon)
		// coupons.PUT("/:id", couponHandler.UpdateCoupon)
		// coupons.DELETE("/:id", couponHandler.DeleteCoupon)
	}
}
