package services

import (
	"context"
	"errors"
	"time"

	"ecom-golang-clean-architecture/internal/domain/user/entities"
	userRepo "ecom-golang-clean-architecture/internal/domain/user/repositories"
	userPorts "ecom-golang-clean-architecture/internal/ports/input/user"
	"github.com/google/uuid"
)

// UserApplicationService implements user business logic
type UserApplicationService struct {
	userRepo    userRepo.UserRepository
	addressRepo userRepo.AddressRepository
	jwtService  JWTService
	emailService EmailService
}

// JWTService interface for token operations
type JWTService interface {
	GenerateTokens(userID uuid.UUID) (accessToken, refreshToken string, expiresIn int64, error)
	ValidateToken(token string) (*TokenClaims, error)
	RefreshTokens(refreshToken string) (accessToken, newRefreshToken string, expiresIn int64, error)
}

// EmailService interface for email operations
type EmailService interface {
	SendVerificationEmail(email, token string) error
	SendPasswordResetEmail(email, token string) error
}

// TokenClaims represents JWT token claims
type TokenClaims struct {
	UserID uuid.UUID `json:"user_id"`
	Email  string    `json:"email"`
	Role   string    `json:"role"`
}

// NewUserApplicationService creates a new user application service
func NewUserApplicationService(
	userRepo userRepo.UserRepository,
	addressRepo userRepo.AddressRepository,
	jwtService JWTService,
	emailService EmailService,
) userPorts.UserService {
	return &UserApplicationService{
		userRepo:     userRepo,
		addressRepo:  addressRepo,
		jwtService:   jwtService,
		emailService: emailService,
	}
}

// Register implements user registration
func (s *UserApplicationService) Register(ctx context.Context, req userPorts.RegisterRequest) (*userPorts.UserResponse, error) {
	// Check if user already exists
	existingUser, _ := s.userRepo.GetByEmail(ctx, req.Email)
	if existingUser != nil {
		return nil, errors.New("user already exists")
	}

	// Create new user entity
	user := &entities.User{
		ID:        uuid.New(),
		Email:     req.Email,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Phone:     req.Phone,
		Role:      "customer",
		IsActive:  true,
		Verified:  false,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Hash password
	if err := user.SetPassword(req.Password); err != nil {
		return nil, err
	}

	// Save user
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, err
	}

	// Send verification email
	verificationToken := uuid.New().String()
	verification := &entities.UserVerification{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     verificationToken,
		Type:      "email_verification",
		ExpiresAt: time.Now().Add(24 * time.Hour),
		CreatedAt: time.Now(),
	}

	if err := s.userRepo.CreateVerification(ctx, verification); err != nil {
		return nil, err
	}

	if err := s.emailService.SendVerificationEmail(user.Email, verificationToken); err != nil {
		// Log error but don't fail registration
	}

	return s.mapUserToResponse(user), nil
}

// Login implements user authentication
func (s *UserApplicationService) Login(ctx context.Context, req userPorts.LoginRequest) (*userPorts.LoginResponse, error) {
	// Get user by email
	user, err := s.userRepo.GetByEmail(ctx, req.Email)
	if err != nil {
		return nil, errors.New("invalid credentials")
	}

	// Check password
	if !user.CheckPassword(req.Password) {
		return nil, errors.New("invalid credentials")
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("account is deactivated")
	}

	// Generate tokens
	accessToken, refreshToken, expiresIn, err := s.jwtService.GenerateTokens(user.ID)
	if err != nil {
		return nil, err
	}

	return &userPorts.LoginResponse{
		User:         s.mapUserToResponse(user),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    expiresIn,
	}, nil
}

// RefreshToken implements token refresh
func (s *UserApplicationService) RefreshToken(ctx context.Context, refreshToken string) (*userPorts.TokenResponse, error) {
	accessToken, newRefreshToken, expiresIn, err := s.jwtService.RefreshTokens(refreshToken)
	if err != nil {
		return nil, err
	}

	return &userPorts.TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    expiresIn,
	}, nil
}

// Logout implements user logout
func (s *UserApplicationService) Logout(ctx context.Context, userID uuid.UUID) error {
	// In a real implementation, you might want to blacklist the token
	// or store logout information
	return nil
}

// GetProfile implements get user profile
func (s *UserApplicationService) GetProfile(ctx context.Context, userID uuid.UUID) (*userPorts.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	return s.mapUserToResponse(user), nil
}

// UpdateProfile implements profile update
func (s *UserApplicationService) UpdateProfile(ctx context.Context, userID uuid.UUID, req userPorts.UpdateProfileRequest) (*userPorts.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}
	if req.LastName != "" {
		user.LastName = req.LastName
	}
	if req.Phone != "" {
		user.Phone = req.Phone
	}
	user.UpdatedAt = time.Now()

	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, err
	}

	return s.mapUserToResponse(user), nil
}

// ChangePassword implements password change
func (s *UserApplicationService) ChangePassword(ctx context.Context, userID uuid.UUID, req userPorts.ChangePasswordRequest) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	// Verify current password
	if !user.CheckPassword(req.CurrentPassword) {
		return errors.New("current password is incorrect")
	}

	// Set new password
	if err := user.SetPassword(req.NewPassword); err != nil {
		return err
	}

	return s.userRepo.Update(ctx, user)
}

// SendVerificationEmail implements email verification
func (s *UserApplicationService) SendVerificationEmail(ctx context.Context, userID uuid.UUID) error {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return err
	}

	if user.Verified {
		return errors.New("email already verified")
	}

	verificationToken := uuid.New().String()
	verification := &entities.UserVerification{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     verificationToken,
		Type:      "email_verification",
		ExpiresAt: time.Now().Add(24 * time.Hour),
		CreatedAt: time.Now(),
	}

	if err := s.userRepo.CreateVerification(ctx, verification); err != nil {
		return err
	}

	return s.emailService.SendVerificationEmail(user.Email, verificationToken)
}

// VerifyEmail implements email verification
func (s *UserApplicationService) VerifyEmail(ctx context.Context, token string) error {
	verification, err := s.userRepo.GetVerificationByToken(ctx, token)
	if err != nil {
		return errors.New("invalid verification token")
	}

	if verification.ExpiresAt.Before(time.Now()) {
		return errors.New("verification token expired")
	}

	return s.userRepo.UpdateVerificationStatus(ctx, verification.UserID, true)
}

// RequestPasswordReset implements password reset request
func (s *UserApplicationService) RequestPasswordReset(ctx context.Context, email string) error {
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		// Don't reveal if email exists
		return nil
	}

	resetToken := uuid.New().String()
	verification := &entities.UserVerification{
		ID:        uuid.New(),
		UserID:    user.ID,
		Token:     resetToken,
		Type:      "password_reset",
		ExpiresAt: time.Now().Add(1 * time.Hour),
		CreatedAt: time.Now(),
	}

	if err := s.userRepo.CreateVerification(ctx, verification); err != nil {
		return err
	}

	return s.emailService.SendPasswordResetEmail(user.Email, resetToken)
}

// ResetPassword implements password reset
func (s *UserApplicationService) ResetPassword(ctx context.Context, token, newPassword string) error {
	verification, err := s.userRepo.GetVerificationByToken(ctx, token)
	if err != nil {
		return errors.New("invalid reset token")
	}

	if verification.ExpiresAt.Before(time.Now()) {
		return errors.New("reset token expired")
	}

	user, err := s.userRepo.GetByID(ctx, verification.UserID)
	if err != nil {
		return err
	}

	if err := user.SetPassword(newPassword); err != nil {
		return err
	}

	return s.userRepo.Update(ctx, user)
}

// Admin operations (simplified implementations)
func (s *UserApplicationService) GetUsers(ctx context.Context, req userPorts.GetUsersRequest) (*userPorts.UsersListResponse, error) {
	// Implementation for admin to get users list
	users, err := s.userRepo.List(ctx, req.Limit, req.Offset)
	if err != nil {
		return nil, err
	}

	total, err := s.userRepo.Count(ctx)
	if err != nil {
		return nil, err
	}

	userResponses := make([]*userPorts.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = s.mapUserToResponse(user)
	}

	return &userPorts.UsersListResponse{
		Users:  userResponses,
		Total:  total,
		Limit:  req.Limit,
		Offset: req.Offset,
	}, nil
}

func (s *UserApplicationService) GetUserByID(ctx context.Context, userID uuid.UUID) (*userPorts.UserResponse, error) {
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	return s.mapUserToResponse(user), nil
}

func (s *UserApplicationService) UpdateUserRole(ctx context.Context, userID uuid.UUID, role string) error {
	return s.userRepo.UpdateRole(ctx, userID, role)
}

func (s *UserApplicationService) UpdateUserStatus(ctx context.Context, userID uuid.UUID, isActive bool) error {
	return s.userRepo.UpdateStatus(ctx, userID, isActive)
}

func (s *UserApplicationService) DeleteUser(ctx context.Context, userID uuid.UUID) error {
	return s.userRepo.Delete(ctx, userID)
}

// Helper method to map entity to response
func (s *UserApplicationService) mapUserToResponse(user *entities.User) *userPorts.UserResponse {
	return &userPorts.UserResponse{
		ID:        user.ID,
		Email:     user.Email,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Phone:     user.Phone,
		Role:      user.Role,
		IsActive:  user.IsActive,
		Verified:  user.Verified,
		CreatedAt: user.CreatedAt.Format(time.RFC3339),
		UpdatedAt: user.UpdatedAt.Format(time.RFC3339),
	}
}
