package entities

import (
	"errors"
	"time"

	"ecom-golang-clean-architecture/pkg/decimal"
	"github.com/google/uuid"
)

var (
	ErrCartItemNotFound     = errors.New("cart item not found")
	ErrInvalidQuantity      = errors.New("invalid quantity")
	ErrCartEmpty           = errors.New("cart is empty")
	ErrPriceChanged        = errors.New("product price has changed")
)

// Cart represents a shopping cart
type Cart struct {
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	UserID    uuid.UUID  `json:"user_id" gorm:"type:uuid;not null;index"`
	User      User       `json:"user" gorm:"foreignKey:UserID"`
	Items     []CartItem `json:"items" gorm:"foreignKey:CartID"`
	CreatedAt time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time  `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for Cart entity
func (Cart) TableName() string {
	return "carts"
}

// CartItem represents an item in the shopping cart
type CartItem struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CartID    uuid.UUID `json:"cart_id" gorm:"type:uuid;not null;index"`
	ProductID uuid.UUID `json:"product_id" gorm:"type:uuid;not null;index"`
	Product   Product   `json:"product" gorm:"foreignKey:ProductID"`
	Quantity  int       `json:"quantity" gorm:"not null" validate:"required,gt=0"`
	Price     float64   `json:"price" gorm:"not null"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for CartItem entity
func (CartItem) TableName() string {
	return "cart_items"
}

// GetTotal calculates the total amount of the cart
func (c *Cart) GetTotal() float64 {
	total := 0.0
	for _, item := range c.Items {
		total += item.GetSubtotal()
	}
	return total
}

// GetItemCount returns the total number of items in the cart
func (c *Cart) GetItemCount() int {
	count := 0
	for _, item := range c.Items {
		count += item.Quantity
	}
	return count
}

// IsEmpty checks if the cart is empty
func (c *Cart) IsEmpty() bool {
	return len(c.Items) == 0
}

// HasItem checks if the cart contains a specific product
func (c *Cart) HasItem(productID uuid.UUID) bool {
	for _, item := range c.Items {
		if item.ProductID == productID {
			return true
		}
	}
	return false
}

// GetItem returns a cart item by product ID
func (c *Cart) GetItem(productID uuid.UUID) *CartItem {
	for i := range c.Items {
		if c.Items[i].ProductID == productID {
			return &c.Items[i]
		}
	}
	return nil
}

// AddItem adds an item to the cart or updates quantity if it exists
func (c *Cart) AddItem(productID uuid.UUID, quantity int, price float64) error {
	if quantity <= 0 {
		return ErrInvalidQuantity
	}

	if existingItem := c.GetItem(productID); existingItem != nil {
		// Update price if it has changed
		existingItem.Price = price
		existingItem.Quantity += quantity
		existingItem.UpdatedAt = time.Now()
	} else {
		newItem := CartItem{
			ID:        uuid.New(),
			CartID:    c.ID,
			ProductID: productID,
			Quantity:  quantity,
			Price:     price,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		c.Items = append(c.Items, newItem)
	}
	c.UpdatedAt = time.Now()
	return nil
}

// UpdateItemPrice updates the price of a cart item
func (c *Cart) UpdateItemPrice(productID uuid.UUID, newPrice float64) error {
	item := c.GetItem(productID)
	if item == nil {
		return ErrCartItemNotFound
	}

	item.Price = newPrice
	item.UpdatedAt = time.Now()
	c.UpdatedAt = time.Now()

	return nil
}

// ValidatePrices validates cart item prices against current product prices
func (c *Cart) ValidatePrices(getCurrentPrice func(uuid.UUID) (float64, error)) error {
	for i := range c.Items {
		currentPrice, err := getCurrentPrice(c.Items[i].ProductID)
		if err != nil {
			return err
		}

		// Use decimal comparison for price validation
		cartPriceDecimal := decimal.NewFromFloat(c.Items[i].Price)
		currentPriceDecimal := decimal.NewFromFloat(currentPrice)

		if !cartPriceDecimal.Equal(currentPriceDecimal) {
			return ErrPriceChanged
		}
	}

	return nil
}

// SyncPrices updates cart item prices to current product prices
func (c *Cart) SyncPrices(getCurrentPrice func(uuid.UUID) (float64, error)) error {
	for i := range c.Items {
		currentPrice, err := getCurrentPrice(c.Items[i].ProductID)
		if err != nil {
			return err
		}

		c.Items[i].Price = currentPrice
		c.Items[i].UpdatedAt = time.Now()
	}

	c.UpdatedAt = time.Now()
	return nil
}

// RemoveItem removes an item from the cart
func (c *Cart) RemoveItem(productID uuid.UUID) {
	for i, item := range c.Items {
		if item.ProductID == productID {
			c.Items = append(c.Items[:i], c.Items[i+1:]...)
			c.UpdatedAt = time.Now()
			break
		}
	}
}

// UpdateItemQuantity updates the quantity of a cart item
func (c *Cart) UpdateItemQuantity(productID uuid.UUID, quantity int) {
	if item := c.GetItem(productID); item != nil {
		if quantity <= 0 {
			c.RemoveItem(productID)
		} else {
			item.Quantity = quantity
			item.UpdatedAt = time.Now()
			c.UpdatedAt = time.Now()
		}
	}
}

// Clear removes all items from the cart
func (c *Cart) Clear() {
	c.Items = []CartItem{}
	c.UpdatedAt = time.Now()
}

// GetSubtotal calculates the subtotal for a cart item using decimal arithmetic
func (ci *CartItem) GetSubtotal() float64 {
	priceDecimal := decimal.NewFromFloat(ci.Price)
	quantityDecimal := decimal.NewFromInt(int64(ci.Quantity))
	subtotal := priceDecimal.Mul(quantityDecimal)
	return subtotal.Float64()
}

// GetSubtotalDecimal returns subtotal as decimal for precise calculations
func (ci *CartItem) GetSubtotalDecimal() decimal.Decimal {
	priceDecimal := decimal.NewFromFloat(ci.Price)
	quantityDecimal := decimal.NewFromInt(int64(ci.Quantity))
	return priceDecimal.Mul(quantityDecimal)
}

// ValidateItem validates cart item data
func (ci *CartItem) ValidateItem() error {
	if ci.Quantity <= 0 {
		return ErrInvalidQuantity
	}

	if ci.Price < 0 {
		return errors.New("price cannot be negative")
	}

	return nil
}
