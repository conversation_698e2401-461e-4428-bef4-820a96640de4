package entities

import (
	"errors"
	"time"

	"ecom-golang-clean-architecture/pkg/decimal"
	"github.com/google/uuid"
)

var (
	ErrInvalidRefundAmount         = errors.New("invalid refund amount")
	ErrRefundAmountExceedsPayment  = errors.New("refund amount exceeds payment amount")
	ErrPaymentNotRefundable        = errors.New("payment is not refundable")
)

// PaymentMethod represents the payment method
type PaymentMethod string

const (
	PaymentMethodCreditCard PaymentMethod = "credit_card"
	PaymentMethodDebitCard  PaymentMethod = "debit_card"
	PaymentMethodPayPal     PaymentMethod = "paypal"
	PaymentMethodStripe     PaymentMethod = "stripe"
	PaymentMethodBankTransfer PaymentMethod = "bank_transfer"
	PaymentMethodCash       PaymentMethod = "cash"
)

// Payment represents a payment transaction
type Payment struct {
	ID                uuid.UUID     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OrderID           uuid.UUID     `json:"order_id" gorm:"type:uuid;not null;index"`
	UserID            uuid.UUID     `json:"user_id" gorm:"type:uuid;not null;index"`
	Amount            float64       `json:"amount" gorm:"not null" validate:"required,gt=0"`
	Currency          string        `json:"currency" gorm:"default:'USD'"`
	Method            PaymentMethod `json:"method" gorm:"not null" validate:"required"`
	Status            PaymentStatus `json:"status" gorm:"default:'pending'"`
	TransactionID     string        `json:"transaction_id" gorm:"index"`
	ExternalID        string        `json:"external_id" gorm:"index"`
	GatewayResponse   string        `json:"gateway_response" gorm:"type:text"`
	FailureReason     string        `json:"failure_reason"`
	ProcessedAt       *time.Time    `json:"processed_at"`
	RefundedAt        *time.Time    `json:"refunded_at"`
	RefundAmount      float64       `json:"refund_amount" gorm:"default:0"`
	CreatedAt         time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time     `json:"updated_at" gorm:"autoUpdateTime"`
}

// TableName returns the table name for Payment entity
func (Payment) TableName() string {
	return "payments"
}

// IsSuccessful checks if the payment is successful
func (p *Payment) IsSuccessful() bool {
	return p.Status == PaymentStatusPaid
}

// IsFailed checks if the payment failed
func (p *Payment) IsFailed() bool {
	return p.Status == PaymentStatusFailed
}

// IsPending checks if the payment is pending
func (p *Payment) IsPending() bool {
	return p.Status == PaymentStatusPending
}

// IsRefunded checks if the payment is refunded
func (p *Payment) IsRefunded() bool {
	return p.Status == PaymentStatusRefunded
}

// MarkAsProcessed marks the payment as processed
func (p *Payment) MarkAsProcessed(transactionID string) {
	p.Status = PaymentStatusPaid
	p.TransactionID = transactionID
	now := time.Now()
	p.ProcessedAt = &now
	p.UpdatedAt = now
}

// MarkAsFailed marks the payment as failed
func (p *Payment) MarkAsFailed(reason string) {
	p.Status = PaymentStatusFailed
	p.FailureReason = reason
	p.UpdatedAt = time.Now()
}

// AddRefund adds a refund to the payment using decimal arithmetic
func (p *Payment) AddRefund(amount float64) error {
	if amount <= 0 {
		return ErrInvalidRefundAmount
	}

	// Use decimal arithmetic for precision
	paymentDecimal := decimal.NewFromFloat(p.Amount)
	refundDecimal := decimal.NewFromFloat(amount)
	currentRefundDecimal := decimal.NewFromFloat(p.RefundAmount)

	newRefundTotal := currentRefundDecimal.Add(refundDecimal)

	if newRefundTotal.GreaterThan(paymentDecimal) {
		return ErrRefundAmountExceedsPayment
	}

	p.RefundAmount = newRefundTotal.Float64()

	// Check if fully refunded using decimal comparison
	if newRefundTotal.GreaterThanOrEqual(paymentDecimal) {
		p.Status = PaymentStatusRefunded
		now := time.Now()
		p.RefundedAt = &now
	}

	p.UpdatedAt = time.Now()
	return nil
}

// GetRefundableAmount returns the amount that can still be refunded
func (p *Payment) GetRefundableAmount() float64 {
	paymentDecimal := decimal.NewFromFloat(p.Amount)
	refundedDecimal := decimal.NewFromFloat(p.RefundAmount)

	remaining := paymentDecimal.Sub(refundedDecimal)
	return remaining.Float64()
}

// CanRefund checks if the payment can be refunded
func (p *Payment) CanRefund(amount float64) error {
	if !p.IsSuccessful() {
		return ErrPaymentNotRefundable
	}

	if p.IsRefunded() {
		return ErrPaymentNotRefundable
	}

	if amount <= 0 {
		return ErrInvalidRefundAmount
	}

	if amount > p.GetRefundableAmount() {
		return ErrRefundAmountExceedsPayment
	}

	return nil
}

// IsPartiallyRefunded checks if payment is partially refunded
func (p *Payment) IsPartiallyRefunded() bool {
	refundDecimal := decimal.NewFromFloat(p.RefundAmount)
	return refundDecimal.IsPositive() && p.Status != PaymentStatusRefunded
}

// GetRefundPercentage returns the percentage of payment that has been refunded
func (p *Payment) GetRefundPercentage() float64 {
	if p.Amount == 0 {
		return 0
	}

	paymentDecimal := decimal.NewFromFloat(p.Amount)
	refundDecimal := decimal.NewFromFloat(p.RefundAmount)

	percentage, err := refundDecimal.Percentage(paymentDecimal)
	if err != nil {
		return 0
	}

	return percentage.Float64()
}

// Refund represents a payment refund
type Refund struct {
	ID            uuid.UUID     `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	PaymentID     uuid.UUID     `json:"payment_id" gorm:"type:uuid;not null;index"`
	Amount        float64       `json:"amount" gorm:"not null" validate:"required,gt=0"`
	Reason        string        `json:"reason" gorm:"not null"`
	Status        RefundStatus  `json:"status" gorm:"default:'pending'"`
	TransactionID string        `json:"transaction_id" gorm:"index"`
	ExternalID    string        `json:"external_id" gorm:"index"`
	ProcessedAt   *time.Time    `json:"processed_at"`
	CreatedAt     time.Time     `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt     time.Time     `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Payment *Payment `json:"payment,omitempty" gorm:"foreignKey:PaymentID"`
}

// RefundStatus represents the refund status
type RefundStatus string

const (
	RefundStatusPending   RefundStatus = "pending"
	RefundStatusCompleted RefundStatus = "completed"
	RefundStatusFailed    RefundStatus = "failed"
	RefundStatusCancelled RefundStatus = "cancelled"
)

// TableName returns the table name for Refund entity
func (Refund) TableName() string {
	return "refunds"
}

// IsCompleted checks if the refund is completed
func (r *Refund) IsCompleted() bool {
	return r.Status == RefundStatusCompleted
}

// MarkAsCompleted marks the refund as completed
func (r *Refund) MarkAsCompleted(transactionID string) {
	r.Status = RefundStatusCompleted
	r.TransactionID = transactionID
	now := time.Now()
	r.ProcessedAt = &now
	r.UpdatedAt = now
}

// MarkAsFailed marks the refund as failed
func (r *Refund) MarkAsFailed() {
	r.Status = RefundStatusFailed
	r.UpdatedAt = time.Now()
}

// GetRemainingRefundAmount returns the remaining amount that can be refunded
func (p *Payment) GetRemainingRefundAmount() float64 {
	return p.Amount - p.RefundAmount
}

// CanBeRefunded checks if the payment can be refunded
func (p *Payment) CanBeRefunded() bool {
	return p.Status == PaymentStatusPaid && p.RefundAmount < p.Amount
}
