package services

import (
	"errors"
	"fmt"

	"ecom-golang-clean-architecture/internal/domain/entities"
)

var (
	ErrInvalidStateTransition = errors.New("invalid state transition")
	ErrOrderNotFound         = errors.New("order not found")
	ErrInvalidOrderState     = errors.New("invalid order state")
)

// OrderStateMachine manages order state transitions
type OrderStateMachine interface {
	CanTransition(from, to entities.OrderStatus) bool
	ValidateTransition(order *entities.Order, newStatus entities.OrderStatus) error
	TransitionTo(order *entities.Order, newStatus entities.OrderStatus) error
	GetAllowedTransitions(currentStatus entities.OrderStatus) []entities.OrderStatus
	GetNextStates(currentStatus entities.OrderStatus) []entities.OrderStatus
}

type orderStateMachine struct {
	transitions map[entities.OrderStatus][]entities.OrderStatus
}

// NewOrderStateMachine creates a new order state machine
func NewOrderStateMachine() OrderStateMachine {
	transitions := map[entities.OrderStatus][]entities.OrderStatus{
		entities.OrderStatusPending: {
			entities.OrderStatusConfirmed,
			entities.OrderStatusCancelled,
		},
		entities.OrderStatusConfirmed: {
			entities.OrderStatusProcessing,
			entities.OrderStatusCancelled,
		},
		entities.OrderStatusProcessing: {
			entities.OrderStatusReadyToShip,
			entities.OrderStatusCancelled,
		},
		entities.OrderStatusReadyToShip: {
			entities.OrderStatusShipped,
			entities.OrderStatusCancelled,
		},
		entities.OrderStatusShipped: {
			entities.OrderStatusOutForDelivery,
			entities.OrderStatusDelivered,
			entities.OrderStatusReturned,
		},
		entities.OrderStatusOutForDelivery: {
			entities.OrderStatusDelivered,
			entities.OrderStatusReturned,
		},
		entities.OrderStatusDelivered: {
			entities.OrderStatusReturned,
			entities.OrderStatusExchanged,
		},
		entities.OrderStatusCancelled: {
			entities.OrderStatusRefunded,
		},
		entities.OrderStatusReturned: {
			entities.OrderStatusRefunded,
			entities.OrderStatusExchanged,
		},
		entities.OrderStatusRefunded: {
			// Terminal state - no transitions allowed
		},
		entities.OrderStatusExchanged: {
			// Terminal state - no transitions allowed
		},
	}

	return &orderStateMachine{
		transitions: transitions,
	}
}

// CanTransition checks if transition from one state to another is allowed
func (sm *orderStateMachine) CanTransition(from, to entities.OrderStatus) bool {
	allowedStates, exists := sm.transitions[from]
	if !exists {
		return false
	}

	for _, allowedState := range allowedStates {
		if allowedState == to {
			return true
		}
	}

	return false
}

// ValidateTransition validates if the transition is allowed and conditions are met
func (sm *orderStateMachine) ValidateTransition(order *entities.Order, newStatus entities.OrderStatus) error {
	if order == nil {
		return ErrOrderNotFound
	}

	// Check if transition is allowed
	if !sm.CanTransition(order.Status, newStatus) {
		return fmt.Errorf("%w: cannot transition from %s to %s", 
			ErrInvalidStateTransition, order.Status, newStatus)
	}

	// Additional business rule validations
	switch newStatus {
	case entities.OrderStatusConfirmed:
		if order.PaymentStatus != entities.PaymentStatusPaid {
			return errors.New("order cannot be confirmed without payment")
		}
		if !order.InventoryReserved {
			return errors.New("order cannot be confirmed without inventory reservation")
		}

	case entities.OrderStatusProcessing:
		if order.PaymentStatus != entities.PaymentStatusPaid {
			return errors.New("order cannot be processed without payment")
		}

	case entities.OrderStatusReadyToShip:
		if order.FulfillmentStatus != entities.FulfillmentStatusPacked {
			return errors.New("order must be packed before ready to ship")
		}

	case entities.OrderStatusShipped:
		if order.TrackingNumber == "" {
			return errors.New("tracking number required for shipped status")
		}
		if order.Carrier == "" {
			return errors.New("carrier required for shipped status")
		}

	case entities.OrderStatusDelivered:
		if order.Status != entities.OrderStatusShipped && 
		   order.Status != entities.OrderStatusOutForDelivery {
			return errors.New("order must be shipped before delivery")
		}

	case entities.OrderStatusCancelled:
		if order.Status == entities.OrderStatusDelivered {
			return errors.New("cannot cancel delivered order")
		}

	case entities.OrderStatusRefunded:
		if order.PaymentStatus != entities.PaymentStatusRefunded {
			return errors.New("payment must be refunded before order refund status")
		}
	}

	return nil
}

// TransitionTo transitions order to new status
func (sm *orderStateMachine) TransitionTo(order *entities.Order, newStatus entities.OrderStatus) error {
	if err := sm.ValidateTransition(order, newStatus); err != nil {
		return err
	}

	oldStatus := order.Status
	order.Status = newStatus

	// Update related fields based on new status
	switch newStatus {
	case entities.OrderStatusConfirmed:
		if order.FulfillmentStatus == entities.FulfillmentStatusPending {
			order.FulfillmentStatus = entities.FulfillmentStatusProcessing
		}

	case entities.OrderStatusProcessing:
		order.FulfillmentStatus = entities.FulfillmentStatusProcessing

	case entities.OrderStatusReadyToShip:
		order.FulfillmentStatus = entities.FulfillmentStatusPacked

	case entities.OrderStatusShipped:
		order.FulfillmentStatus = entities.FulfillmentStatusShipped

	case entities.OrderStatusDelivered:
		order.FulfillmentStatus = entities.FulfillmentStatusDelivered

	case entities.OrderStatusCancelled:
		order.FulfillmentStatus = entities.FulfillmentStatusCancelled

	case entities.OrderStatusReturned:
		order.FulfillmentStatus = entities.FulfillmentStatusReturned
	}

	// Log state transition (in real app, this would use proper logging)
	fmt.Printf("Order %s transitioned from %s to %s\n", 
		order.OrderNumber, oldStatus, newStatus)

	return nil
}

// GetAllowedTransitions returns all allowed transitions from current status
func (sm *orderStateMachine) GetAllowedTransitions(currentStatus entities.OrderStatus) []entities.OrderStatus {
	transitions, exists := sm.transitions[currentStatus]
	if !exists {
		return []entities.OrderStatus{}
	}

	// Return a copy to prevent modification
	result := make([]entities.OrderStatus, len(transitions))
	copy(result, transitions)
	return result
}

// GetNextStates returns possible next states (alias for GetAllowedTransitions)
func (sm *orderStateMachine) GetNextStates(currentStatus entities.OrderStatus) []entities.OrderStatus {
	return sm.GetAllowedTransitions(currentStatus)
}

// IsTerminalState checks if the status is a terminal state
func (sm *orderStateMachine) IsTerminalState(status entities.OrderStatus) bool {
	transitions := sm.GetAllowedTransitions(status)
	return len(transitions) == 0
}

// CanCancel checks if order can be cancelled from current state
func (sm *orderStateMachine) CanCancel(currentStatus entities.OrderStatus) bool {
	return sm.CanTransition(currentStatus, entities.OrderStatusCancelled)
}

// CanRefund checks if order can be refunded from current state
func (sm *orderStateMachine) CanRefund(currentStatus entities.OrderStatus) bool {
	return sm.CanTransition(currentStatus, entities.OrderStatusRefunded)
}

// GetStateDescription returns human-readable description of state
func (sm *orderStateMachine) GetStateDescription(status entities.OrderStatus) string {
	descriptions := map[entities.OrderStatus]string{
		entities.OrderStatusPending:        "Order is pending payment confirmation",
		entities.OrderStatusConfirmed:      "Order is confirmed and payment received",
		entities.OrderStatusProcessing:     "Order is being processed",
		entities.OrderStatusReadyToShip:    "Order is packed and ready to ship",
		entities.OrderStatusShipped:        "Order has been shipped",
		entities.OrderStatusOutForDelivery: "Order is out for delivery",
		entities.OrderStatusDelivered:      "Order has been delivered",
		entities.OrderStatusCancelled:      "Order has been cancelled",
		entities.OrderStatusRefunded:       "Order has been refunded",
		entities.OrderStatusReturned:       "Order has been returned",
		entities.OrderStatusExchanged:      "Order has been exchanged",
	}

	if desc, exists := descriptions[status]; exists {
		return desc
	}

	return "Unknown status"
}

// ValidateOrderForShipping validates order is ready for shipping
func (sm *orderStateMachine) ValidateOrderForShipping(order *entities.Order) error {
	if order.Status != entities.OrderStatusReadyToShip {
		return errors.New("order must be in ready-to-ship status")
	}

	if order.ShippingAddress == nil {
		return errors.New("shipping address is required")
	}

	if len(order.Items) == 0 {
		return errors.New("order must have items")
	}

	return nil
}

// ValidateOrderForDelivery validates order is ready for delivery
func (sm *orderStateMachine) ValidateOrderForDelivery(order *entities.Order) error {
	if order.Status != entities.OrderStatusShipped && 
	   order.Status != entities.OrderStatusOutForDelivery {
		return errors.New("order must be shipped before delivery")
	}

	if order.TrackingNumber == "" {
		return errors.New("tracking number is required for delivery")
	}

	return nil
}
