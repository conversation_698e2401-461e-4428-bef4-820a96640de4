package repositories

import (
	"context"
	"ecom-golang-clean-architecture/internal/domain/product/entities"
	"github.com/google/uuid"
)

// ProductRepository defines the interface for product data operations
type ProductRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, product *entities.Product) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Product, error)
	GetBySKU(ctx context.Context, sku string) (*entities.Product, error)
	Update(ctx context.Context, product *entities.Product) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, limit, offset int) ([]*entities.Product, error)
	Count(ctx context.Context) (int64, error)
	Search(ctx context.Context, query string, limit, offset int) ([]*entities.Product, error)
	
	// Category operations
	GetByCategory(ctx context.Context, categoryID uuid.UUID, limit, offset int) ([]*entities.Product, error)
	GetFeatured(ctx context.Context, limit int) ([]*entities.Product, error)
	GetOnSale(ctx context.Context, limit, offset int) ([]*entities.Product, error)
	
	// Stock operations
	UpdateStock(ctx context.Context, productID uuid.UUID, quantity int) error
	GetLowStock(ctx context.Context, threshold int) ([]*entities.Product, error)
	ReserveStock(ctx context.Context, productID uuid.UUID, quantity int) error
	ReleaseStock(ctx context.Context, productID uuid.UUID, quantity int) error
	
	// Pricing operations
	UpdatePrice(ctx context.Context, productID uuid.UUID, price float64) error
	GetByPriceRange(ctx context.Context, minPrice, maxPrice float64, limit, offset int) ([]*entities.Product, error)
}

// CategoryRepository defines the interface for category operations
type CategoryRepository interface {
	Create(ctx context.Context, category *entities.Category) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Category, error)
	GetBySlug(ctx context.Context, slug string) (*entities.Category, error)
	Update(ctx context.Context, category *entities.Category) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*entities.Category, error)
	GetChildren(ctx context.Context, parentID uuid.UUID) ([]*entities.Category, error)
	GetTree(ctx context.Context) ([]*entities.Category, error)
}

// InventoryRepository defines the interface for inventory operations
type InventoryRepository interface {
	Create(ctx context.Context, inventory *entities.Inventory) error
	GetByProductID(ctx context.Context, productID uuid.UUID) (*entities.Inventory, error)
	Update(ctx context.Context, inventory *entities.Inventory) error
	UpdateQuantity(ctx context.Context, productID uuid.UUID, quantity int) error
	GetLowStock(ctx context.Context, threshold int) ([]*entities.Inventory, error)
	CreateMovement(ctx context.Context, movement *entities.InventoryMovement) error
	GetMovements(ctx context.Context, productID uuid.UUID, limit, offset int) ([]*entities.InventoryMovement, error)
}
