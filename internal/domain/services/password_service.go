package services

import (
	"errors"
	"regexp"
	"unicode"

	"golang.org/x/crypto/bcrypt"
)

var (
	ErrPasswordTooShort    = errors.New("password must be at least 8 characters long")
	ErrPasswordTooWeak     = errors.New("password must contain at least one uppercase letter, one lowercase letter, one number, and one special character")
	ErrPasswordTooCommon   = errors.New("password is too common")
)

// PasswordService handles password operations
type PasswordService interface {
	HashPassword(password string) (string, error)
	CheckPassword(password, hashedPassword string) error
	ValidatePasswordStrength(password string) error
}

type passwordService struct {
	cost int
}

// NewPasswordService creates a new password service
func NewPasswordService() PasswordService {
	return &passwordService{
		cost: bcrypt.DefaultCost,
	}
}

// HashPassword hashes a password using bcrypt
func (s *passwordService) HashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), s.cost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// CheckPassword checks if a password matches the hashed password
func (s *passwordService) CheckPassword(password, hashedPassword string) error {
	return bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
}

// ValidatePasswordStrength validates password strength
func (s *passwordService) ValidatePasswordStrength(password string) error {
	if len(password) < 8 {
		return ErrPasswordTooShort
	}

	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case unicode.IsUpper(char):
			hasUpper = true
		case unicode.IsLower(char):
			hasLower = true
		case unicode.IsNumber(char):
			hasNumber = true
		case unicode.IsPunct(char) || unicode.IsSymbol(char):
			hasSpecial = true
		}
	}

	if !hasUpper || !hasLower || !hasNumber || !hasSpecial {
		return ErrPasswordTooWeak
	}

	// Check for common passwords
	if s.isCommonPassword(password) {
		return ErrPasswordTooCommon
	}

	return nil
}

// isCommonPassword checks if password is in common passwords list
func (s *passwordService) isCommonPassword(password string) bool {
	commonPasswords := []string{
		"password", "123456", "123456789", "12345678", "12345",
		"1234567", "admin", "qwerty", "abc123", "password123",
		"admin123", "root", "toor", "pass", "test",
	}

	for _, common := range commonPasswords {
		if password == common {
			return true
		}
	}

	// Check for simple patterns
	simplePatterns := []string{
		`^(.)\1+$`,        // All same character
		`^(012|123|234|345|456|567|678|789|890)+$`, // Sequential numbers
		`^(abc|bcd|cde|def|efg|fgh|ghi|hij|ijk|jkl|klm|lmn|mno|nop|opq|pqr|qrs|rst|stu|tuv|uvw|vwx|wxy|xyz)+$`, // Sequential letters
	}

	for _, pattern := range simplePatterns {
		if matched, _ := regexp.MatchString(pattern, password); matched {
			return true
		}
	}

	return false
}
