package services

import (
	"errors"
	"time"

	"ecom-golang-clean-architecture/internal/domain/entities"
	"ecom-golang-clean-architecture/pkg/decimal"
)

var (
	ErrPaymentAmountMismatch = errors.New("payment amount does not match order total")
	ErrInvalidRefundAmount   = errors.New("invalid refund amount")
	ErrRefundExceedsPayment  = errors.New("refund amount exceeds payment amount")
	ErrPaymentAlreadyProcessed = errors.New("payment already processed")
	ErrPaymentNotFound       = errors.New("payment not found")
)

// PaymentService handles payment business logic
type PaymentService interface {
	ValidatePaymentAmount(paymentAmount, orderTotal float64) error
	CalculateRefund(payment *entities.Payment, refundAmount float64) (*RefundCalculation, error)
	ProcessRefund(payment *entities.Payment, refundAmount float64, reason string) error
	CanRefund(payment *entities.Payment, amount float64) error
	GetRefundableAmount(payment *entities.Payment) float64
}

type paymentService struct{}

// RefundCalculation represents refund calculation result
type RefundCalculation struct {
	RefundAmount     decimal.Decimal
	RemainingAmount  decimal.Decimal
	TotalRefunded    decimal.Decimal
	IsFullRefund     bool
	IsPartialRefund  bool
}

// NewPaymentService creates a new payment service
func NewPaymentService() PaymentService {
	return &paymentService{}
}

// ValidatePaymentAmount validates payment amount against order total
func (s *paymentService) ValidatePaymentAmount(paymentAmount, orderTotal float64) error {
	paymentDecimal := decimal.NewFromFloat(paymentAmount)
	orderDecimal := decimal.NewFromFloat(orderTotal)
	
	// Use decimal comparison to avoid floating point precision issues
	if !paymentDecimal.Equal(orderDecimal) {
		return ErrPaymentAmountMismatch
	}
	
	return nil
}

// CalculateRefund calculates refund details
func (s *paymentService) CalculateRefund(payment *entities.Payment, refundAmount float64) (*RefundCalculation, error) {
	if refundAmount <= 0 {
		return nil, ErrInvalidRefundAmount
	}
	
	paymentDecimal := decimal.NewFromFloat(payment.Amount)
	refundDecimal := decimal.NewFromFloat(refundAmount)
	currentRefundedDecimal := decimal.NewFromFloat(payment.RefundAmount)
	
	// Check if refund amount is valid
	totalRefundDecimal := currentRefundedDecimal.Add(refundDecimal)
	if totalRefundDecimal.GreaterThan(paymentDecimal) {
		return nil, ErrRefundExceedsPayment
	}
	
	remainingDecimal := paymentDecimal.Sub(totalRefundDecimal)
	
	return &RefundCalculation{
		RefundAmount:    refundDecimal,
		RemainingAmount: remainingDecimal,
		TotalRefunded:   totalRefundDecimal,
		IsFullRefund:    totalRefundDecimal.Equal(paymentDecimal),
		IsPartialRefund: totalRefundDecimal.LessThan(paymentDecimal) && totalRefundDecimal.GreaterThan(decimal.Zero()),
	}, nil
}

// ProcessRefund processes a refund for a payment
func (s *paymentService) ProcessRefund(payment *entities.Payment, refundAmount float64, reason string) error {
	calculation, err := s.CalculateRefund(payment, refundAmount)
	if err != nil {
		return err
	}
	
	// Update payment refund amount
	payment.RefundAmount = calculation.TotalRefunded.Float64()
	
	// Update payment status if fully refunded
	if calculation.IsFullRefund {
		payment.Status = entities.PaymentStatusRefunded
		now := time.Now()
		payment.RefundedAt = &now
	}
	
	payment.UpdatedAt = time.Now()
	
	return nil
}

// CanRefund checks if a payment can be refunded
func (s *paymentService) CanRefund(payment *entities.Payment, amount float64) error {
	if !payment.IsSuccessful() {
		return errors.New("can only refund successful payments")
	}
	
	if payment.IsRefunded() {
		return errors.New("payment is already fully refunded")
	}
	
	if amount <= 0 {
		return ErrInvalidRefundAmount
	}
	
	refundableAmount := s.GetRefundableAmount(payment)
	if amount > refundableAmount {
		return ErrRefundExceedsPayment
	}
	
	return nil
}

// GetRefundableAmount returns the amount that can still be refunded
func (s *paymentService) GetRefundableAmount(payment *entities.Payment) float64 {
	paymentDecimal := decimal.NewFromFloat(payment.Amount)
	refundedDecimal := decimal.NewFromFloat(payment.RefundAmount)
	
	remaining := paymentDecimal.Sub(refundedDecimal)
	return remaining.Float64()
}

// PaymentCalculator handles payment calculations
type PaymentCalculator struct{}

// NewPaymentCalculator creates a new payment calculator
func NewPaymentCalculator() *PaymentCalculator {
	return &PaymentCalculator{}
}

// CalculateOrderTotal calculates order total with tax and shipping
func (c *PaymentCalculator) CalculateOrderTotal(subtotal, taxRate, shippingCost, discountAmount float64) (decimal.Decimal, decimal.Decimal, decimal.Decimal) {
	subtotalDecimal := decimal.NewFromFloat(subtotal)
	taxRateDecimal := decimal.NewFromFloat(taxRate)
	shippingDecimal := decimal.NewFromFloat(shippingCost)
	discountDecimal := decimal.NewFromFloat(discountAmount)
	
	// Calculate tax on subtotal minus discount
	taxableAmount := subtotalDecimal.Sub(discountDecimal)
	if taxableAmount.IsNegative() {
		taxableAmount = decimal.Zero()
	}
	
	taxAmount := taxableAmount.Mul(taxRateDecimal)
	
	// Calculate total
	total := subtotalDecimal.Add(taxAmount).Add(shippingDecimal).Sub(discountDecimal)
	
	// Ensure total is not negative
	if total.IsNegative() {
		total = decimal.Zero()
	}
	
	return subtotalDecimal, taxAmount, total
}

// CalculateItemTotal calculates total for order items
func (c *PaymentCalculator) CalculateItemTotal(items []entities.CartItem) decimal.Decimal {
	total := decimal.Zero()
	
	for _, item := range items {
		itemPrice := decimal.NewFromFloat(item.Price)
		quantity := decimal.NewFromInt(int64(item.Quantity))
		itemTotal := itemPrice.Mul(quantity)
		total = total.Add(itemTotal)
	}
	
	return total
}

// ApplyDiscount applies discount to amount
func (c *PaymentCalculator) ApplyDiscount(amount decimal.Decimal, discountPercent float64) decimal.Decimal {
	if discountPercent <= 0 || discountPercent > 100 {
		return amount
	}
	
	discountRate := decimal.NewFromFloat(discountPercent / 100)
	discountAmount := amount.Mul(discountRate)
	
	return amount.Sub(discountAmount)
}

// CalculateTip calculates tip amount
func (c *PaymentCalculator) CalculateTip(amount decimal.Decimal, tipPercent float64) decimal.Decimal {
	if tipPercent <= 0 {
		return decimal.Zero()
	}
	
	tipRate := decimal.NewFromFloat(tipPercent / 100)
	return amount.Mul(tipRate)
}

// SplitPayment splits payment amount across multiple methods
func (c *PaymentCalculator) SplitPayment(totalAmount decimal.Decimal, splits []PaymentSplit) ([]PaymentSplit, error) {
	if len(splits) == 0 {
		return nil, errors.New("no payment splits provided")
	}
	
	totalSplit := decimal.Zero()
	result := make([]PaymentSplit, len(splits))
	
	// Calculate amounts for percentage-based splits
	for i, split := range splits {
		result[i] = split
		
		if split.IsPercentage {
			if split.Percentage <= 0 || split.Percentage > 100 {
				return nil, errors.New("invalid percentage")
			}
			
			rate := decimal.NewFromFloat(split.Percentage / 100)
			result[i].Amount = totalAmount.Mul(rate)
		} else {
			result[i].Amount = decimal.NewFromFloat(split.FixedAmount)
		}
		
		totalSplit = totalSplit.Add(result[i].Amount)
	}
	
	// Validate total doesn't exceed payment amount
	if totalSplit.GreaterThan(totalAmount) {
		return nil, errors.New("split amounts exceed total payment amount")
	}
	
	return result, nil
}

// PaymentSplit represents a payment split
type PaymentSplit struct {
	Method       entities.PaymentMethod
	Amount       decimal.Decimal
	FixedAmount  float64
	Percentage   float64
	IsPercentage bool
}
