package repositories

import (
	"context"
	"ecom-golang-clean-architecture/internal/domain/user/entities"
	"github.com/google/uuid"
)

// UserRepository defines the interface for user data operations
type UserRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, user *entities.User) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.User, error)
	GetByEmail(ctx context.Context, email string) (*entities.User, error)
	Update(ctx context.Context, user *entities.User) error
	Delete(ctx context.Context, id uuid.UUID) error
	
	// Query operations
	List(ctx context.Context, limit, offset int) ([]*entities.User, error)
	Count(ctx context.Context) (int64, error)
	
	// Authentication related
	GetByEmailAndPassword(ctx context.Context, email, password string) (*entities.User, error)
	UpdatePassword(ctx context.Context, userID uuid.UUID, hashedPassword string) error
	
	// Profile operations
	UpdateProfile(ctx context.Context, userID uuid.UUID, profile *entities.UserProfile) error
	GetProfile(ctx context.Context, userID uuid.UUID) (*entities.UserProfile, error)
	
	// Verification operations
	CreateVerification(ctx context.Context, verification *entities.UserVerification) error
	GetVerificationByToken(ctx context.Context, token string) (*entities.UserVerification, error)
	UpdateVerificationStatus(ctx context.Context, userID uuid.UUID, verified bool) error
	
	// Admin operations
	GetByRole(ctx context.Context, role string, limit, offset int) ([]*entities.User, error)
	UpdateRole(ctx context.Context, userID uuid.UUID, role string) error
	UpdateStatus(ctx context.Context, userID uuid.UUID, isActive bool) error
}

// AddressRepository defines the interface for address operations
type AddressRepository interface {
	Create(ctx context.Context, address *entities.Address) error
	GetByID(ctx context.Context, id uuid.UUID) (*entities.Address, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*entities.Address, error)
	Update(ctx context.Context, address *entities.Address) error
	Delete(ctx context.Context, id uuid.UUID) error
	SetDefault(ctx context.Context, userID, addressID uuid.UUID) error
	GetDefault(ctx context.Context, userID uuid.UUID) (*entities.Address, error)
}
