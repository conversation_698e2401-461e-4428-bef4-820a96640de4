package database

import (
	"fmt"
	"log"

	"ecom-golang-clean-architecture/internal/domain/entities"
	"gorm.io/gorm"
)

// AutoMigrate runs database migrations
func AutoMigrate(db *gorm.DB) error {
	log.Println("Starting database migrations...")

	// Define migration order to handle foreign key dependencies
	migrationOrder := []interface{}{
		// Core entities first
		&entities.User{},
		&entities.UserProfile{},
		&entities.UserSession{},
		&entities.UserActivity{},
		&entities.UserVerification{},

		// Product related entities
		&entities.Category{},
		&entities.Product{},
		&entities.ProductImage{},
		&entities.ProductAttribute{},
		&entities.ProductVariant{},
		&entities.ProductVariantAttribute{},

		// Inventory
		&entities.StockReservation{},

		// Cart and Wishlist
		&entities.Cart{},
		&entities.CartItem{},
		&entities.Wishlist{},

		// Orders
		&entities.Order{},
		&entities.OrderItem{},

		// Payments
		&entities.Payment{},

		// Reviews
		&entities.Review{},
		&entities.ReviewVote{},
		&entities.ProductRating{},

		// Coupons
		&entities.Coupon{},
		&entities.CouponUsage{},
	}

	// Run auto migration
	for _, model := range migrationOrder {
		if err := db.AutoMigrate(model); err != nil {
			return fmt.Errorf("failed to migrate %T: %w", model, err)
		}
		log.Printf("Migrated: %T", model)
	}

	// Add custom constraints and indexes
	if err := addCustomConstraints(db); err != nil {
		return fmt.Errorf("failed to add custom constraints: %w", err)
	}

	// Add indexes for performance
	if err := addPerformanceIndexes(db); err != nil {
		return fmt.Errorf("failed to add performance indexes: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// addCustomConstraints adds custom database constraints
func addCustomConstraints(db *gorm.DB) error {
	log.Println("Adding custom constraints...")

	constraints := []string{
		// User constraints
		`ALTER TABLE users ADD CONSTRAINT check_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')`,
		`ALTER TABLE users ADD CONSTRAINT check_phone_format CHECK (phone IS NULL OR phone ~* '^\+?[1-9]\d{1,14}$')`,
		
		// Product constraints
		`ALTER TABLE products ADD CONSTRAINT check_price_positive CHECK (price > 0)`,
		`ALTER TABLE products ADD CONSTRAINT check_sale_price_valid CHECK (sale_price IS NULL OR sale_price > 0)`,
		`ALTER TABLE products ADD CONSTRAINT check_compare_price_valid CHECK (compare_price IS NULL OR compare_price > 0)`,
		`ALTER TABLE products ADD CONSTRAINT check_stock_non_negative CHECK (stock >= 0)`,
		`ALTER TABLE products ADD CONSTRAINT check_low_stock_threshold_non_negative CHECK (low_stock_threshold >= 0)`,
		`ALTER TABLE products ADD CONSTRAINT check_sale_dates CHECK (sale_start_date IS NULL OR sale_end_date IS NULL OR sale_start_date <= sale_end_date)`,
		
		// Order constraints
		`ALTER TABLE orders ADD CONSTRAINT check_order_totals_non_negative CHECK (subtotal >= 0 AND tax_amount >= 0 AND shipping_amount >= 0 AND discount_amount >= 0 AND tip_amount >= 0 AND total >= 0)`,
		`ALTER TABLE order_items ADD CONSTRAINT check_order_item_quantity_positive CHECK (quantity > 0)`,
		`ALTER TABLE order_items ADD CONSTRAINT check_order_item_price_non_negative CHECK (price >= 0)`,
		
		// Payment constraints
		`ALTER TABLE payments ADD CONSTRAINT check_payment_amount_positive CHECK (amount > 0)`,
		`ALTER TABLE payments ADD CONSTRAINT check_refund_amount_non_negative CHECK (refund_amount >= 0)`,
		`ALTER TABLE payments ADD CONSTRAINT check_refund_not_exceed_payment CHECK (refund_amount <= amount)`,
		
		// Cart constraints
		`ALTER TABLE cart_items ADD CONSTRAINT check_cart_quantity_positive CHECK (quantity > 0)`,
		`ALTER TABLE cart_items ADD CONSTRAINT check_cart_price_non_negative CHECK (price >= 0)`,
		
		// Review constraints
		`ALTER TABLE reviews ADD CONSTRAINT check_rating_range CHECK (rating >= 1 AND rating <= 5)`,
		`ALTER TABLE reviews ADD CONSTRAINT check_vote_counts_non_negative CHECK (helpful_count >= 0 AND not_helpful_count >= 0)`,
		
		// Coupon constraints
		`ALTER TABLE coupons ADD CONSTRAINT check_coupon_value_positive CHECK (value > 0)`,
		`ALTER TABLE coupons ADD CONSTRAINT check_usage_limits_non_negative CHECK (usage_limit IS NULL OR usage_limit >= 0)`,
		`ALTER TABLE coupons ADD CONSTRAINT check_user_usage_limit_non_negative CHECK (user_usage_limit IS NULL OR user_usage_limit >= 0)`,
		`ALTER TABLE coupons ADD CONSTRAINT check_minimum_order_non_negative CHECK (minimum_order_amount IS NULL OR minimum_order_amount >= 0)`,
		`ALTER TABLE coupons ADD CONSTRAINT check_coupon_dates CHECK (starts_at IS NULL OR expires_at IS NULL OR starts_at <= expires_at)`,
	}

	for _, constraint := range constraints {
		if err := db.Exec(constraint).Error; err != nil {
			// Log warning but don't fail if constraint already exists
			log.Printf("Warning: Failed to add constraint (may already exist): %v", err)
		}
	}

	log.Println("Custom constraints added")
	return nil
}

// addPerformanceIndexes adds indexes for better query performance
func addPerformanceIndexes(db *gorm.DB) error {
	log.Println("Adding performance indexes...")

	indexes := []string{
		// User indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email_verified ON users(email, email_verified)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_status_active ON users(status, is_active)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_created_at ON users(created_at)`,
		
		// Product indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_status_active ON products(status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_category_status ON products(category_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_price_range ON products(price) WHERE status = 'active'`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_stock_status ON products(stock_status, track_quantity)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_created_at ON products(created_at)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_name_search ON products USING gin(to_tsvector('english', name))`,
		
		// Order indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_user_status ON orders(user_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_status_created ON orders(status, created_at)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_payment_status ON orders(payment_status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_fulfillment_status ON orders(fulfillment_status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_order_number ON orders(order_number)`,
		
		// Order items indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_product ON order_items(order_id, product_id)`,
		
		// Payment indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_order_status ON payments(order_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_transaction_id ON payments(transaction_id)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_created_at ON payments(created_at)`,
		
		// Cart indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_carts_user_status ON carts(user_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_cart_items_cart_product ON cart_items(cart_id, product_id)`,
		
		// Review indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_product_status ON reviews(product_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_user_product ON reviews(user_id, product_id)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_reviews_rating_helpful ON reviews(rating, helpful_count)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_review_votes_review_user ON review_votes(review_id, user_id)`,
		
		// Coupon indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_code_status ON coupons(code, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupons_expires_at ON coupons(expires_at) WHERE status = 'active'`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_coupon_usage_coupon_user ON coupon_usage(coupon_id, user_id)`,
		
		// Session indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_active ON user_sessions(user_id, is_active)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)`,
		
		// Stock reservation indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stock_reservations_product_status ON stock_reservations(product_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_stock_reservations_expires_at ON stock_reservations(expires_at)`,
		
		// Category indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_parent_active ON categories(parent_id, is_active)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_slug ON categories(slug)`,
		
		// Address indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_addresses_user_default ON user_addresses(user_id, is_default)`,
		
		// Wishlist indexes
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlists_user_status ON wishlists(user_id, status)`,
		`CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_wishlist_items_wishlist_product ON wishlist_items(wishlist_id, product_id)`,
	}

	for _, index := range indexes {
		if err := db.Exec(index).Error; err != nil {
			// Log warning but don't fail if index already exists
			log.Printf("Warning: Failed to create index (may already exist): %v", err)
		}
	}

	log.Println("Performance indexes added")
	return nil
}

// DropAllTables drops all tables (use with caution!)
func DropAllTables(db *gorm.DB) error {
	log.Println("WARNING: Dropping all tables...")

	// Get all table names
	var tables []string
	if err := db.Raw(`
		SELECT tablename 
		FROM pg_tables 
		WHERE schemaname = 'public' 
		AND tablename NOT LIKE 'pg_%'
		AND tablename != 'schema_migrations'
	`).Scan(&tables).Error; err != nil {
		return fmt.Errorf("failed to get table names: %w", err)
	}

	// Drop tables with CASCADE to handle foreign key constraints
	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table)).Error; err != nil {
			log.Printf("Warning: Failed to drop table %s: %v", table, err)
		} else {
			log.Printf("Dropped table: %s", table)
		}
	}

	log.Println("All tables dropped")
	return nil
}

// ValidateSchema validates database schema integrity
func ValidateSchema(db *gorm.DB) error {
	log.Println("Validating database schema...")

	// Check for missing foreign key constraints
	missingFKs := []string{
		"SELECT 'user_profiles.user_id -> users.id' WHERE NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name LIKE '%user_profiles_user_id_fkey%')",
		"SELECT 'orders.user_id -> users.id' WHERE NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name LIKE '%orders_user_id_fkey%')",
		"SELECT 'order_items.order_id -> orders.id' WHERE NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name LIKE '%order_items_order_id_fkey%')",
		"SELECT 'payments.order_id -> orders.id' WHERE NOT EXISTS (SELECT 1 FROM information_schema.table_constraints WHERE constraint_name LIKE '%payments_order_id_fkey%')",
	}

	for _, query := range missingFKs {
		var result string
		if err := db.Raw(query).Scan(&result).Error; err == nil && result != "" {
			log.Printf("Warning: Missing foreign key constraint: %s", result)
		}
	}

	log.Println("Schema validation completed")
	return nil
}
