package product

import (
	"context"
	"ecom-golang-clean-architecture/internal/domain/product/entities"
	"github.com/google/uuid"
)

// ProductService defines the use cases for product management
type ProductService interface {
	// Product operations
	CreateProduct(ctx context.Context, req CreateProductRequest) (*ProductResponse, error)
	GetProduct(ctx context.Context, id uuid.UUID) (*ProductResponse, error)
	GetProductBySKU(ctx context.Context, sku string) (*ProductResponse, error)
	UpdateProduct(ctx context.Context, id uuid.UUID, req UpdateProductRequest) (*ProductResponse, error)
	DeleteProduct(ctx context.Context, id uuid.UUID) error
	
	// Product listing and search
	ListProducts(ctx context.Context, req ListProductsRequest) (*ProductsListResponse, error)
	SearchProducts(ctx context.Context, req SearchProductsRequest) (*ProductsListResponse, error)
	GetFeaturedProducts(ctx context.Context, limit int) ([]*ProductResponse, error)
	GetProductsOnSale(ctx context.Context, req ListProductsRequest) (*ProductsListResponse, error)
	
	// Category operations
	GetProductsByCategory(ctx context.Context, categoryID uuid.UUID, req ListProductsRequest) (*ProductsListResponse, error)
	
	// Stock operations
	UpdateStock(ctx context.Context, productID uuid.UUID, quantity int) error
	GetLowStockProducts(ctx context.Context, threshold int) ([]*ProductResponse, error)
	ReserveStock(ctx context.Context, productID uuid.UUID, quantity int) error
	ReleaseStock(ctx context.Context, productID uuid.UUID, quantity int) error
	
	// Pricing operations
	UpdatePrice(ctx context.Context, productID uuid.UUID, price float64) error
	GetProductsByPriceRange(ctx context.Context, minPrice, maxPrice float64, req ListProductsRequest) (*ProductsListResponse, error)
}

// CategoryService defines the use cases for category management
type CategoryService interface {
	CreateCategory(ctx context.Context, req CreateCategoryRequest) (*CategoryResponse, error)
	GetCategory(ctx context.Context, id uuid.UUID) (*CategoryResponse, error)
	GetCategoryBySlug(ctx context.Context, slug string) (*CategoryResponse, error)
	UpdateCategory(ctx context.Context, id uuid.UUID, req UpdateCategoryRequest) (*CategoryResponse, error)
	DeleteCategory(ctx context.Context, id uuid.UUID) error
	ListCategories(ctx context.Context, req ListCategoriesRequest) (*CategoriesListResponse, error)
	GetCategoryTree(ctx context.Context) ([]*CategoryResponse, error)
	GetSubCategories(ctx context.Context, parentID uuid.UUID) ([]*CategoryResponse, error)
}

// Request DTOs
type CreateProductRequest struct {
	Name            string                 `json:"name" validate:"required"`
	Description     string                 `json:"description"`
	ShortDescription string                `json:"short_description"`
	SKU             string                 `json:"sku" validate:"required"`
	Price           float64                `json:"price" validate:"required,gt=0"`
	ComparePrice    *float64               `json:"compare_price,omitempty"`
	SalePrice       *float64               `json:"sale_price,omitempty"`
	CategoryID      uuid.UUID              `json:"category_id" validate:"required"`
	BrandID         *uuid.UUID             `json:"brand_id,omitempty"`
	Weight          *float64               `json:"weight,omitempty"`
	Dimensions      map[string]interface{} `json:"dimensions,omitempty"`
	Images          []string               `json:"images,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	MetaTitle       string                 `json:"meta_title,omitempty"`
	MetaDescription string                 `json:"meta_description,omitempty"`
	Status          string                 `json:"status,omitempty"`
	Visibility      string                 `json:"visibility,omitempty"`
	TrackQuantity   bool                   `json:"track_quantity"`
	Stock           int                    `json:"stock,omitempty"`
	LowStockThreshold int                  `json:"low_stock_threshold,omitempty"`
	AllowBackorder  bool                   `json:"allow_backorder"`
}

type UpdateProductRequest struct {
	Name            *string                `json:"name,omitempty"`
	Description     *string                `json:"description,omitempty"`
	ShortDescription *string               `json:"short_description,omitempty"`
	Price           *float64               `json:"price,omitempty"`
	ComparePrice    *float64               `json:"compare_price,omitempty"`
	SalePrice       *float64               `json:"sale_price,omitempty"`
	CategoryID      *uuid.UUID             `json:"category_id,omitempty"`
	BrandID         *uuid.UUID             `json:"brand_id,omitempty"`
	Weight          *float64               `json:"weight,omitempty"`
	Dimensions      map[string]interface{} `json:"dimensions,omitempty"`
	Images          []string               `json:"images,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	MetaTitle       *string                `json:"meta_title,omitempty"`
	MetaDescription *string                `json:"meta_description,omitempty"`
	Status          *string                `json:"status,omitempty"`
	Visibility      *string                `json:"visibility,omitempty"`
	TrackQuantity   *bool                  `json:"track_quantity,omitempty"`
	Stock           *int                   `json:"stock,omitempty"`
	LowStockThreshold *int                 `json:"low_stock_threshold,omitempty"`
	AllowBackorder  *bool                  `json:"allow_backorder,omitempty"`
}

type ListProductsRequest struct {
	CategoryID *uuid.UUID `json:"category_id,omitempty"`
	BrandID    *uuid.UUID `json:"brand_id,omitempty"`
	Status     string     `json:"status,omitempty"`
	MinPrice   *float64   `json:"min_price,omitempty"`
	MaxPrice   *float64   `json:"max_price,omitempty"`
	Tags       []string   `json:"tags,omitempty"`
	SortBy     string     `json:"sort_by,omitempty"`
	SortOrder  string     `json:"sort_order,omitempty"`
	Limit      int        `json:"limit,omitempty"`
	Offset     int        `json:"offset,omitempty"`
}

type SearchProductsRequest struct {
	Query     string   `json:"query" validate:"required"`
	CategoryID *uuid.UUID `json:"category_id,omitempty"`
	MinPrice  *float64 `json:"min_price,omitempty"`
	MaxPrice  *float64 `json:"max_price,omitempty"`
	Tags      []string `json:"tags,omitempty"`
	Limit     int      `json:"limit,omitempty"`
	Offset    int      `json:"offset,omitempty"`
}

type CreateCategoryRequest struct {
	Name        string     `json:"name" validate:"required"`
	Description string     `json:"description,omitempty"`
	Slug        string     `json:"slug,omitempty"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	Image       string     `json:"image,omitempty"`
	MetaTitle   string     `json:"meta_title,omitempty"`
	MetaDescription string `json:"meta_description,omitempty"`
	IsActive    bool       `json:"is_active"`
	SortOrder   int        `json:"sort_order,omitempty"`
}

type UpdateCategoryRequest struct {
	Name        *string    `json:"name,omitempty"`
	Description *string    `json:"description,omitempty"`
	Slug        *string    `json:"slug,omitempty"`
	ParentID    *uuid.UUID `json:"parent_id,omitempty"`
	Image       *string    `json:"image,omitempty"`
	MetaTitle   *string    `json:"meta_title,omitempty"`
	MetaDescription *string `json:"meta_description,omitempty"`
	IsActive    *bool      `json:"is_active,omitempty"`
	SortOrder   *int       `json:"sort_order,omitempty"`
}

type ListCategoriesRequest struct {
	ParentID  *uuid.UUID `json:"parent_id,omitempty"`
	IsActive  *bool      `json:"is_active,omitempty"`
	Limit     int        `json:"limit,omitempty"`
	Offset    int        `json:"offset,omitempty"`
}

// Response DTOs
type ProductResponse struct {
	ID              uuid.UUID              `json:"id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	ShortDescription string                `json:"short_description"`
	SKU             string                 `json:"sku"`
	Price           float64                `json:"price"`
	ComparePrice    *float64               `json:"compare_price,omitempty"`
	SalePrice       *float64               `json:"sale_price,omitempty"`
	CurrentPrice    float64                `json:"current_price"`
	DiscountPercent float64                `json:"discount_percent"`
	CategoryID      uuid.UUID              `json:"category_id"`
	Category        *CategoryResponse      `json:"category,omitempty"`
	BrandID         *uuid.UUID             `json:"brand_id,omitempty"`
	Weight          *float64               `json:"weight,omitempty"`
	Dimensions      map[string]interface{} `json:"dimensions,omitempty"`
	Images          []string               `json:"images"`
	Tags            []string               `json:"tags"`
	MetaTitle       string                 `json:"meta_title"`
	MetaDescription string                 `json:"meta_description"`
	Status          string                 `json:"status"`
	Visibility      string                 `json:"visibility"`
	TrackQuantity   bool                   `json:"track_quantity"`
	Stock           int                    `json:"stock"`
	StockStatus     string                 `json:"stock_status"`
	LowStockThreshold int                  `json:"low_stock_threshold"`
	AllowBackorder  bool                   `json:"allow_backorder"`
	IsOnSale        bool                   `json:"is_on_sale"`
	IsLowStock      bool                   `json:"is_low_stock"`
	IsInStock       bool                   `json:"is_in_stock"`
	CreatedAt       string                 `json:"created_at"`
	UpdatedAt       string                 `json:"updated_at"`
}

type ProductsListResponse struct {
	Products []*ProductResponse `json:"products"`
	Total    int64              `json:"total"`
	Limit    int                `json:"limit"`
	Offset   int                `json:"offset"`
}

type CategoryResponse struct {
	ID          uuid.UUID           `json:"id"`
	Name        string              `json:"name"`
	Description string              `json:"description"`
	Slug        string              `json:"slug"`
	ParentID    *uuid.UUID          `json:"parent_id,omitempty"`
	Image       string              `json:"image"`
	MetaTitle   string              `json:"meta_title"`
	MetaDescription string          `json:"meta_description"`
	IsActive    bool                `json:"is_active"`
	SortOrder   int                 `json:"sort_order"`
	ProductCount int                `json:"product_count"`
	Children    []*CategoryResponse `json:"children,omitempty"`
	CreatedAt   string              `json:"created_at"`
	UpdatedAt   string              `json:"updated_at"`
}

type CategoriesListResponse struct {
	Categories []*CategoryResponse `json:"categories"`
	Total      int64               `json:"total"`
	Limit      int                 `json:"limit"`
	Offset     int                 `json:"offset"`
}
