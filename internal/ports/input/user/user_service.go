package user

import (
	"context"
	"ecom-golang-clean-architecture/internal/domain/user/entities"
	"github.com/google/uuid"
)

// UserService defines the use cases for user management
type UserService interface {
	// Authentication
	Register(ctx context.Context, req RegisterRequest) (*UserResponse, error)
	Login(ctx context.Context, req LoginRequest) (*LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error)
	Logout(ctx context.Context, userID uuid.UUID) error
	
	// Profile management
	GetProfile(ctx context.Context, userID uuid.UUID) (*UserResponse, error)
	UpdateProfile(ctx context.Context, userID uuid.UUID, req UpdateProfileRequest) (*UserResponse, error)
	ChangePassword(ctx context.Context, userID uuid.UUID, req ChangePasswordRequest) error
	
	// Email verification
	SendVerificationEmail(ctx context.Context, userID uuid.UUID) error
	VerifyEmail(ctx context.Context, token string) error
	
	// Password reset
	RequestPasswordReset(ctx context.Context, email string) error
	ResetPassword(ctx context.Context, token, newPassword string) error
	
	// Admin operations
	GetUsers(ctx context.Context, req GetUsersRequest) (*UsersListResponse, error)
	GetUserByID(ctx context.Context, userID uuid.UUID) (*UserResponse, error)
	UpdateUserRole(ctx context.Context, userID uuid.UUID, role string) error
	UpdateUserStatus(ctx context.Context, userID uuid.UUID, isActive bool) error
	DeleteUser(ctx context.Context, userID uuid.UUID) error
}

// AddressService defines the use cases for address management
type AddressService interface {
	CreateAddress(ctx context.Context, userID uuid.UUID, req CreateAddressRequest) (*AddressResponse, error)
	GetAddresses(ctx context.Context, userID uuid.UUID) ([]*AddressResponse, error)
	GetAddress(ctx context.Context, userID, addressID uuid.UUID) (*AddressResponse, error)
	UpdateAddress(ctx context.Context, userID, addressID uuid.UUID, req UpdateAddressRequest) (*AddressResponse, error)
	DeleteAddress(ctx context.Context, userID, addressID uuid.UUID) error
	SetDefaultAddress(ctx context.Context, userID, addressID uuid.UUID) error
	GetDefaultAddress(ctx context.Context, userID uuid.UUID) (*AddressResponse, error)
}

// Request/Response DTOs
type RegisterRequest struct {
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8"`
	FirstName string `json:"first_name" validate:"required"`
	LastName  string `json:"last_name" validate:"required"`
	Phone     string `json:"phone,omitempty"`
}

type LoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

type LoginResponse struct {
	User         *UserResponse `json:"user"`
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	ExpiresIn    int64         `json:"expires_in"`
}

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
}

type UpdateProfileRequest struct {
	FirstName string `json:"first_name,omitempty"`
	LastName  string `json:"last_name,omitempty"`
	Phone     string `json:"phone,omitempty"`
	Avatar    string `json:"avatar,omitempty"`
}

type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8"`
}

type GetUsersRequest struct {
	Role     string `json:"role,omitempty"`
	IsActive *bool  `json:"is_active,omitempty"`
	Limit    int    `json:"limit,omitempty"`
	Offset   int    `json:"offset,omitempty"`
}

type UserResponse struct {
	ID        uuid.UUID `json:"id"`
	Email     string    `json:"email"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Phone     string    `json:"phone,omitempty"`
	Avatar    string    `json:"avatar,omitempty"`
	Role      string    `json:"role"`
	IsActive  bool      `json:"is_active"`
	Verified  bool      `json:"verified"`
	CreatedAt string    `json:"created_at"`
	UpdatedAt string    `json:"updated_at"`
}

type UsersListResponse struct {
	Users  []*UserResponse `json:"users"`
	Total  int64           `json:"total"`
	Limit  int             `json:"limit"`
	Offset int             `json:"offset"`
}

type CreateAddressRequest struct {
	Type       string `json:"type" validate:"required"`
	FirstName  string `json:"first_name" validate:"required"`
	LastName   string `json:"last_name" validate:"required"`
	Company    string `json:"company,omitempty"`
	Address1   string `json:"address1" validate:"required"`
	Address2   string `json:"address2,omitempty"`
	City       string `json:"city" validate:"required"`
	State      string `json:"state" validate:"required"`
	ZipCode    string `json:"zip_code" validate:"required"`
	Country    string `json:"country" validate:"required"`
	Phone      string `json:"phone,omitempty"`
	IsDefault  bool   `json:"is_default,omitempty"`
}

type UpdateAddressRequest struct {
	Type       string `json:"type,omitempty"`
	FirstName  string `json:"first_name,omitempty"`
	LastName   string `json:"last_name,omitempty"`
	Company    string `json:"company,omitempty"`
	Address1   string `json:"address1,omitempty"`
	Address2   string `json:"address2,omitempty"`
	City       string `json:"city,omitempty"`
	State      string `json:"state,omitempty"`
	ZipCode    string `json:"zip_code,omitempty"`
	Country    string `json:"country,omitempty"`
	Phone      string `json:"phone,omitempty"`
	IsDefault  bool   `json:"is_default,omitempty"`
}

type AddressResponse struct {
	ID        uuid.UUID `json:"id"`
	Type      string    `json:"type"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Company   string    `json:"company,omitempty"`
	Address1  string    `json:"address1"`
	Address2  string    `json:"address2,omitempty"`
	City      string    `json:"city"`
	State     string    `json:"state"`
	ZipCode   string    `json:"zip_code"`
	Country   string    `json:"country"`
	Phone     string    `json:"phone,omitempty"`
	IsDefault bool      `json:"is_default"`
	CreatedAt string    `json:"created_at"`
	UpdatedAt string    `json:"updated_at"`
}
