package decimal

import (
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
)

var (
	ErrInvalidDecimal = errors.New("invalid decimal value")
	ErrDivisionByZero = errors.New("division by zero")
)

// Decimal represents a decimal number with fixed precision
type Decimal struct {
	value int64 // Value in cents (or smallest unit)
	scale int   // Number of decimal places
}

// NewFromFloat creates a new Decimal from float64
func NewFromFloat(value float64) Decimal {
	// Convert to cents to avoid floating point precision issues
	cents := int64(math.Round(value * 100))
	return Decimal{value: cents, scale: 2}
}

// NewFromInt creates a new Decimal from int64
func NewFromInt(value int64) Decimal {
	return Decimal{value: value * 100, scale: 2}
}

// NewFromString creates a new Decimal from string
func NewFromString(value string) (Decimal, error) {
	if value == "" {
		return Decimal{}, ErrInvalidDecimal
	}

	// Handle negative numbers
	negative := false
	if strings.HasPrefix(value, "-") {
		negative = true
		value = value[1:]
	}

	parts := strings.Split(value, ".")
	if len(parts) > 2 {
		return Decimal{}, ErrInvalidDecimal
	}

	intPart, err := strconv.ParseInt(parts[0], 10, 64)
	if err != nil {
		return Decimal{}, ErrInvalidDecimal
	}

	var fracPart int64 = 0
	if len(parts) == 2 {
		// Pad or truncate to 2 decimal places
		frac := parts[1]
		if len(frac) > 2 {
			frac = frac[:2]
		} else if len(frac) == 1 {
			frac += "0"
		}
		
		fracPart, err = strconv.ParseInt(frac, 10, 64)
		if err != nil {
			return Decimal{}, ErrInvalidDecimal
		}
	}

	result := intPart*100 + fracPart
	if negative {
		result = -result
	}

	return Decimal{value: result, scale: 2}, nil
}

// Zero returns a zero decimal
func Zero() Decimal {
	return Decimal{value: 0, scale: 2}
}

// Float64 returns the decimal as float64
func (d Decimal) Float64() float64 {
	return float64(d.value) / math.Pow10(d.scale)
}

// String returns the decimal as string
func (d Decimal) String() string {
	if d.scale == 0 {
		return fmt.Sprintf("%d", d.value)
	}

	divisor := int64(math.Pow10(d.scale))
	intPart := d.value / divisor
	fracPart := d.value % divisor

	if fracPart < 0 {
		fracPart = -fracPart
	}

	return fmt.Sprintf("%d.%0*d", intPart, d.scale, fracPart)
}

// Add adds two decimals
func (d Decimal) Add(other Decimal) Decimal {
	return Decimal{value: d.value + other.value, scale: d.scale}
}

// Sub subtracts two decimals
func (d Decimal) Sub(other Decimal) Decimal {
	return Decimal{value: d.value - other.value, scale: d.scale}
}

// Mul multiplies two decimals
func (d Decimal) Mul(other Decimal) Decimal {
	result := d.value * other.value
	// Adjust for double scaling
	result = result / int64(math.Pow10(d.scale))
	return Decimal{value: result, scale: d.scale}
}

// Div divides two decimals
func (d Decimal) Div(other Decimal) (Decimal, error) {
	if other.value == 0 {
		return Decimal{}, ErrDivisionByZero
	}

	// Scale up to maintain precision
	result := (d.value * int64(math.Pow10(d.scale))) / other.value
	return Decimal{value: result, scale: d.scale}, nil
}

// Equal checks if two decimals are equal
func (d Decimal) Equal(other Decimal) bool {
	return d.value == other.value
}

// GreaterThan checks if decimal is greater than other
func (d Decimal) GreaterThan(other Decimal) bool {
	return d.value > other.value
}

// GreaterThanOrEqual checks if decimal is greater than or equal to other
func (d Decimal) GreaterThanOrEqual(other Decimal) bool {
	return d.value >= other.value
}

// LessThan checks if decimal is less than other
func (d Decimal) LessThan(other Decimal) bool {
	return d.value < other.value
}

// LessThanOrEqual checks if decimal is less than or equal to other
func (d Decimal) LessThanOrEqual(other Decimal) bool {
	return d.value <= other.value
}

// IsZero checks if decimal is zero
func (d Decimal) IsZero() bool {
	return d.value == 0
}

// IsPositive checks if decimal is positive
func (d Decimal) IsPositive() bool {
	return d.value > 0
}

// IsNegative checks if decimal is negative
func (d Decimal) IsNegative() bool {
	return d.value < 0
}

// Abs returns absolute value
func (d Decimal) Abs() Decimal {
	if d.value < 0 {
		return Decimal{value: -d.value, scale: d.scale}
	}
	return d
}

// Round rounds to specified decimal places
func (d Decimal) Round(places int) Decimal {
	if places >= d.scale {
		return d
	}

	factor := int64(math.Pow10(d.scale - places))
	rounded := d.value / factor
	
	// Round to nearest
	remainder := d.value % factor
	if remainder >= factor/2 {
		rounded++
	}

	return Decimal{value: rounded * factor, scale: d.scale}
}

// Truncate truncates to specified decimal places
func (d Decimal) Truncate(places int) Decimal {
	if places >= d.scale {
		return d
	}

	factor := int64(math.Pow10(d.scale - places))
	truncated := (d.value / factor) * factor
	
	return Decimal{value: truncated, scale: d.scale}
}

// Percentage calculates percentage of total
func (d Decimal) Percentage(total Decimal) (Decimal, error) {
	if total.IsZero() {
		return Decimal{}, ErrDivisionByZero
	}

	hundred := NewFromInt(100)
	result, err := d.Mul(hundred).Div(total)
	if err != nil {
		return Decimal{}, err
	}

	return result, nil
}

// Tax calculates tax amount
func (d Decimal) Tax(rate Decimal) Decimal {
	return d.Mul(rate)
}

// Discount calculates discount amount
func (d Decimal) Discount(rate Decimal) Decimal {
	return d.Mul(rate)
}

// ApplyDiscount applies discount and returns final amount
func (d Decimal) ApplyDiscount(discountAmount Decimal) Decimal {
	return d.Sub(discountAmount)
}

// ApplyTax applies tax and returns final amount
func (d Decimal) ApplyTax(taxAmount Decimal) Decimal {
	return d.Add(taxAmount)
}
