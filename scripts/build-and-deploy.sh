#!/bin/bash

# E-commerce Build and Deploy Script
# This script builds the Docker image and deploys the system

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="ecom-golang-clean-architecture"
IMAGE_NAME="ecom-api"
VERSION=${1:-"latest"}
ENVIRONMENT=${2:-"development"}

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Clean up old containers and images
cleanup() {
    log_info "Cleaning up old containers and images..."
    
    # Stop and remove containers
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # Remove old images (keep last 3 versions)
    local old_images=$(docker images "$IMAGE_NAME" --format "table {{.Repository}}:{{.Tag}}" | tail -n +2 | tail -n +4)
    if [ ! -z "$old_images" ]; then
        echo "$old_images" | xargs docker rmi 2>/dev/null || true
        log_info "Removed old images"
    fi
    
    # Clean up dangling images
    docker image prune -f > /dev/null 2>&1 || true
    
    log_success "Cleanup completed"
}

# Build the application
build_app() {
    log_info "Building Go application..."
    
    # Run tests first
    log_info "Running Go tests..."
    if go test ./... -v; then
        log_success "All tests passed"
    else
        log_error "Tests failed. Aborting build."
        exit 1
    fi
    
    # Build for different architectures if needed
    log_info "Building binary..."
    CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -ldflags="-w -s" -o bin/main cmd/api/main.go
    
    if [ -f "bin/main" ]; then
        log_success "Binary built successfully"
    else
        log_error "Failed to build binary"
        exit 1
    fi
}

# Build Docker image
build_docker() {
    log_info "Building Docker image: $IMAGE_NAME:$VERSION"
    
    # Build the image
    if docker build -t "$IMAGE_NAME:$VERSION" -t "$IMAGE_NAME:latest" .; then
        log_success "Docker image built successfully"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
    
    # Show image info
    local image_size=$(docker images "$IMAGE_NAME:$VERSION" --format "table {{.Size}}" | tail -n +2)
    log_info "Image size: $image_size"
}

# Security scan
security_scan() {
    log_info "Running security scan..."
    
    # Check if trivy is available
    if command -v trivy > /dev/null 2>&1; then
        log_info "Scanning image with Trivy..."
        trivy image --exit-code 1 --severity HIGH,CRITICAL "$IMAGE_NAME:$VERSION" || {
            log_warning "Security vulnerabilities found. Review the scan results."
        }
    else
        log_warning "Trivy not available. Skipping security scan."
        log_info "Install Trivy for security scanning: https://github.com/aquasecurity/trivy"
    fi
}

# Validate configuration
validate_config() {
    log_info "Validating configuration..."
    
    # Check if required environment files exist
    if [ ! -f ".env.example" ]; then
        log_warning ".env.example not found"
    fi
    
    # Check if docker-compose.yml is valid
    if docker-compose config > /dev/null 2>&1; then
        log_success "docker-compose.yml is valid"
    else
        log_error "docker-compose.yml is invalid"
        exit 1
    fi
    
    # Check if required directories exist
    mkdir -p uploads logs
    log_info "Created required directories"
}

# Deploy the application
deploy() {
    log_info "Deploying application in $ENVIRONMENT mode..."
    
    case $ENVIRONMENT in
        "development")
            deploy_development
            ;;
        "staging")
            deploy_staging
            ;;
        "production")
            deploy_production
            ;;
        *)
            log_error "Unknown environment: $ENVIRONMENT"
            log_info "Supported environments: development, staging, production"
            exit 1
            ;;
    esac
}

# Development deployment
deploy_development() {
    log_info "Starting development environment..."
    
    # Start services
    docker-compose up -d
    
    # Wait for services to be ready
    log_info "Waiting for services to start..."
    sleep 10
    
    # Check if services are healthy
    check_services_health
    
    log_success "Development environment is ready!"
    log_info "API: http://localhost:8080"
    log_info "MailHog: http://localhost:8025"
    log_info "Database: localhost:5432"
    log_info "Redis: localhost:6379"
}

# Staging deployment
deploy_staging() {
    log_info "Deploying to staging environment..."
    
    # Use staging docker-compose file if it exists
    local compose_file="docker-compose.staging.yml"
    if [ -f "$compose_file" ]; then
        docker-compose -f docker-compose.yml -f "$compose_file" up -d
    else
        log_warning "Staging compose file not found, using development config"
        docker-compose up -d
    fi
    
    check_services_health
    log_success "Staging deployment completed!"
}

# Production deployment
deploy_production() {
    log_info "Deploying to production environment..."
    
    # Additional production checks
    if [ "$VERSION" == "latest" ]; then
        log_error "Cannot deploy 'latest' tag to production. Please specify a version."
        exit 1
    fi
    
    # Check if production compose file exists
    local compose_file="docker-compose.production.yml"
    if [ ! -f "$compose_file" ]; then
        log_error "Production compose file not found: $compose_file"
        exit 1
    fi
    
    # Deploy with production configuration
    docker-compose -f docker-compose.yml -f "$compose_file" up -d
    
    check_services_health
    log_success "Production deployment completed!"
}

# Check services health
check_services_health() {
    log_info "Checking services health..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        local healthy_services=0
        
        # Check API health
        if curl -s http://localhost:8080/health > /dev/null 2>&1; then
            ((healthy_services++))
        fi
        
        # Check database
        if docker exec ecom_postgres pg_isready -U postgres -d ecommerce_db > /dev/null 2>&1; then
            ((healthy_services++))
        fi
        
        # Check Redis
        if docker exec ecom_redis redis-cli ping > /dev/null 2>&1; then
            ((healthy_services++))
        fi
        
        if [ $healthy_services -eq 3 ]; then
            log_success "All services are healthy!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: $healthy_services/3 services healthy"
        sleep 2
        ((attempt++))
    done
    
    log_error "Services failed to become healthy within expected time"
    docker-compose logs --tail=50
    return 1
}

# Run post-deployment tests
run_tests() {
    log_info "Running post-deployment tests..."
    
    if [ -f "scripts/test-system.sh" ]; then
        chmod +x scripts/test-system.sh
        if ./scripts/test-system.sh; then
            log_success "All tests passed!"
        else
            log_error "Some tests failed!"
            return 1
        fi
    else
        log_warning "Test script not found, skipping tests"
    fi
}

# Show deployment info
show_info() {
    log_info "Deployment Information:"
    echo "======================="
    log_info "Project: $PROJECT_NAME"
    log_info "Image: $IMAGE_NAME:$VERSION"
    log_info "Environment: $ENVIRONMENT"
    log_info "Build time: $(date)"
    echo ""
    
    log_info "Running containers:"
    docker-compose ps
    echo ""
    
    log_info "Image details:"
    docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# Main function
main() {
    log_info "E-commerce Build and Deploy Script"
    log_info "=================================="
    log_info "Version: $VERSION"
    log_info "Environment: $ENVIRONMENT"
    echo ""
    
    # Pre-flight checks
    check_docker
    validate_config
    
    # Build process
    build_app
    build_docker
    
    # Security scan (optional)
    if [ "$ENVIRONMENT" == "production" ]; then
        security_scan
    fi
    
    # Clean up old resources
    cleanup
    
    # Deploy
    deploy
    
    # Post-deployment tests
    if [ "$ENVIRONMENT" != "production" ]; then
        run_tests
    fi
    
    # Show deployment info
    show_info
    
    log_success "Build and deployment completed successfully! 🚀"
}

# Script usage
usage() {
    echo "Usage: $0 [VERSION] [ENVIRONMENT]"
    echo ""
    echo "Arguments:"
    echo "  VERSION      Docker image version (default: latest)"
    echo "  ENVIRONMENT  Deployment environment: development|staging|production (default: development)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Build latest for development"
    echo "  $0 v1.0.0 staging           # Build v1.0.0 for staging"
    echo "  $0 v1.0.0 production        # Build v1.0.0 for production"
}

# Handle script arguments
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    usage
    exit 0
fi

# Run main function
main "$@"
