#!/bin/bash

# Test Clean Architecture Structure
# This script validates the new clean architecture implementation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Test structure validation
test_structure() {
    log_info "Testing Clean Architecture Structure..."
    
    # Test domain structure
    if [ -d "internal/domain/user/entities" ] && [ -d "internal/domain/user/repositories" ]; then
        log_success "User domain structure exists"
    else
        log_error "User domain structure missing"
    fi
    
    if [ -d "internal/domain/product/entities" ] && [ -d "internal/domain/product/repositories" ]; then
        log_success "Product domain structure exists"
    else
        log_error "Product domain structure missing"
    fi
    
    if [ -d "internal/domain/order/entities" ] && [ -d "internal/domain/order/repositories" ]; then
        log_success "Order domain structure exists"
    else
        log_error "Order domain structure missing"
    fi
    
    # Test ports structure
    if [ -d "internal/ports/input" ] && [ -d "internal/ports/output" ]; then
        log_success "Ports structure exists"
    else
        log_error "Ports structure missing"
    fi
    
    # Test adapters structure
    if [ -d "internal/adapters/input/http" ] && [ -d "internal/adapters/output/persistence" ]; then
        log_success "Adapters structure exists"
    else
        log_error "Adapters structure missing"
    fi
    
    # Test application layer
    if [ -d "internal/app/services" ]; then
        log_success "Application layer exists"
    else
        log_error "Application layer missing"
    fi
}

# Test file existence
test_files() {
    log_info "Testing key files existence..."
    
    # Domain entities
    if [ -f "internal/domain/user/entities/user.go" ]; then
        log_success "User entity exists in correct location"
    else
        log_error "User entity missing"
    fi
    
    if [ -f "internal/domain/product/entities/product.go" ]; then
        log_success "Product entity exists in correct location"
    else
        log_error "Product entity missing"
    fi
    
    # Repository interfaces
    if [ -f "internal/domain/user/repositories/user_repository.go" ]; then
        log_success "User repository interface exists"
    else
        log_error "User repository interface missing"
    fi
    
    # Use case interfaces
    if [ -f "internal/ports/input/user/user_service.go" ]; then
        log_success "User service interface exists"
    else
        log_error "User service interface missing"
    fi
    
    # Application services
    if [ -f "internal/app/services/user_service.go" ]; then
        log_success "User application service exists"
    else
        log_error "User application service missing"
    fi
    
    # HTTP adapters
    if [ -f "internal/adapters/input/http/user/user_handler.go" ]; then
        log_success "User HTTP handler exists in correct location"
    else
        log_error "User HTTP handler missing"
    fi
    
    # Repository implementations
    if [ -f "internal/adapters/output/persistence/postgres/user_repository.go" ]; then
        log_success "User repository implementation exists"
    else
        log_error "User repository implementation missing"
    fi
    
    # Clean main
    if [ -f "cmd/api/main_clean.go" ]; then
        log_success "Clean architecture main exists"
    else
        log_error "Clean architecture main missing"
    fi
    
    # Router
    if [ -f "internal/adapters/input/http/router.go" ]; then
        log_success "Clean router exists"
    else
        log_error "Clean router missing"
    fi
}

# Test package declarations
test_packages() {
    log_info "Testing package declarations..."
    
    # Check user entity package
    if grep -q "package entities" internal/domain/user/entities/user.go; then
        log_success "User entity has correct package declaration"
    else
        log_error "User entity has incorrect package declaration"
    fi
    
    # Check user handler package
    if grep -q "package user" internal/adapters/input/http/user/user_handler.go; then
        log_success "User handler has correct package declaration"
    else
        log_error "User handler has incorrect package declaration"
    fi
    
    # Check postgres adapter package
    if grep -q "package postgres" internal/adapters/output/persistence/postgres/user_repository.go; then
        log_success "Postgres adapter has correct package declaration"
    else
        log_error "Postgres adapter has incorrect package declaration"
    fi
}

# Test dependency direction
test_dependencies() {
    log_info "Testing dependency directions..."
    
    # Domain should not import infrastructure
    if ! grep -r "internal/infrastructure" internal/domain/ 2>/dev/null; then
        log_success "Domain layer has no infrastructure dependencies"
    else
        log_error "Domain layer has infrastructure dependencies (violation)"
    fi
    
    # Domain should not import adapters
    if ! grep -r "internal/adapters" internal/domain/ 2>/dev/null; then
        log_success "Domain layer has no adapter dependencies"
    else
        log_error "Domain layer has adapter dependencies (violation)"
    fi
    
    # Ports should not import adapters
    if ! grep -r "internal/adapters" internal/ports/ 2>/dev/null; then
        log_success "Ports layer has no adapter dependencies"
    else
        log_error "Ports layer has adapter dependencies (violation)"
    fi
    
    # Application should not import adapters
    if ! grep -r "internal/adapters" internal/app/ 2>/dev/null; then
        log_success "Application layer has no adapter dependencies"
    else
        log_error "Application layer has adapter dependencies (violation)"
    fi
}

# Test compilation
test_compilation() {
    log_info "Testing compilation..."
    
    # Test if clean main compiles
    if go build -o /tmp/test_clean cmd/api/main_clean.go 2>/dev/null; then
        log_success "Clean architecture main compiles successfully"
        rm -f /tmp/test_clean
    else
        log_error "Clean architecture main compilation failed"
    fi
    
    # Test if individual packages compile
    if go build internal/domain/user/entities/... 2>/dev/null; then
        log_success "User domain entities compile"
    else
        log_error "User domain entities compilation failed"
    fi
    
    if go build internal/ports/input/user/... 2>/dev/null; then
        log_success "User ports compile"
    else
        log_error "User ports compilation failed"
    fi
}

# Test clean architecture principles
test_principles() {
    log_info "Testing Clean Architecture principles..."
    
    # Test 1: Dependency Rule
    log_info "Checking Dependency Rule compliance..."
    
    # Test 2: Interface Segregation
    if [ -f "internal/domain/user/repositories/user_repository.go" ]; then
        local interface_count=$(grep -c "interface" internal/domain/user/repositories/user_repository.go)
        if [ "$interface_count" -ge 1 ]; then
            log_success "Repository interfaces are properly segregated"
        else
            log_error "Repository interfaces not found"
        fi
    fi
    
    # Test 3: Single Responsibility
    local user_files=$(find internal/domain/user -name "*.go" | wc -l)
    if [ "$user_files" -ge 3 ]; then
        log_success "User domain has proper file separation"
    else
        log_warning "User domain might need more file separation"
    fi
}

# Test documentation
test_documentation() {
    log_info "Testing documentation..."
    
    if [ -f "CLEAN_ARCHITECTURE_STRUCTURE.md" ]; then
        log_success "Clean architecture documentation exists"
    else
        log_error "Clean architecture documentation missing"
    fi
    
    if [ -f "ARCHITECTURE_REFACTOR_PROPOSAL.md" ]; then
        log_success "Architecture refactor proposal exists"
    else
        log_error "Architecture refactor proposal missing"
    fi
}

# Main test function
run_tests() {
    log_info "Clean Architecture Validation Test Suite"
    echo "========================================"
    
    test_structure
    test_files
    test_packages
    test_dependencies
    test_compilation
    test_principles
    test_documentation
    
    echo "========================================"
    log_info "Test Summary:"
    log_info "Tests Passed: $TESTS_PASSED"
    log_info "Tests Failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All Clean Architecture tests passed! 🎉"
        log_info "The codebase now follows Clean Architecture principles correctly!"
        return 0
    else
        log_error "Some Clean Architecture tests failed! ❌"
        log_info "Please review the failed tests and fix the issues."
        return 1
    fi
}

# Show structure
show_structure() {
    log_info "Current Clean Architecture Structure:"
    echo "===================================="
    
    echo "📁 Domain Layer:"
    find internal/domain -type d | head -20 | sed 's/^/  /'
    
    echo ""
    echo "📁 Ports Layer:"
    find internal/ports -type d | head -10 | sed 's/^/  /'
    
    echo ""
    echo "📁 Application Layer:"
    find internal/app -type d | head -10 | sed 's/^/  /'
    
    echo ""
    echo "📁 Adapters Layer:"
    find internal/adapters -type d | head -15 | sed 's/^/  /'
    
    echo ""
    echo "📁 Key Files:"
    echo "  cmd/api/main_clean.go"
    echo "  internal/adapters/input/http/router.go"
    echo "  internal/app/services/user_service.go"
    echo "  internal/domain/user/entities/user.go"
    echo "  internal/ports/input/user/user_service.go"
}

# Main execution
main() {
    case "${1:-test}" in
        "test")
            run_tests
            ;;
        "structure")
            show_structure
            ;;
        "help")
            echo "Usage: $0 [test|structure|help]"
            echo "  test      - Run Clean Architecture validation tests"
            echo "  structure - Show current structure"
            echo "  help      - Show this help"
            ;;
        *)
            echo "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Script entry point
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
