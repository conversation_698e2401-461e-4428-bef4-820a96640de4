#!/bin/bash

# E-commerce System Test Script
# This script tests the entire system functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8080/api/v1"
HEALTH_URL="http://localhost:8080/health"

# Test results
TESTS_PASSED=0
TESTS_FAILED=0

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    ((TESTS_PASSED++))
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    ((TESTS_FAILED++))
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Wait for service to be ready
wait_for_service() {
    local url=$1
    local service_name=$2
    local max_attempts=30
    local attempt=1

    log_info "Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            log_success "$service_name is ready!"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        ((attempt++))
    done
    
    log_error "$service_name failed to start within expected time"
    return 1
}

# Test API endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5
    local auth_token=$6

    log_info "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ ! -z "$auth_token" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $auth_token'"
    fi
    
    if [ ! -z "$data" ]; then
        curl_cmd="$curl_cmd -H 'Content-Type: application/json' -d '$data'"
    fi
    
    curl_cmd="$curl_cmd '$API_BASE_URL$endpoint'"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        log_success "$description - Status: $status_code"
        echo "$body" | jq . 2>/dev/null || echo "$body"
        return 0
    else
        log_error "$description - Expected: $expected_status, Got: $status_code"
        echo "Response: $body"
        return 1
    fi
}

# Main test function
run_tests() {
    log_info "Starting E-commerce System Tests..."
    echo "=================================="
    
    # Test 1: Health Check
    test_endpoint "GET" "/health" "200" "Health Check" "" ""
    
    # Test 2: User Registration
    local user_data='{
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "phone": "+**********"
    }'
    test_endpoint "POST" "/auth/register" "201" "User Registration" "$user_data" ""
    
    # Test 3: User Login
    local login_data='{
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }'
    local login_response=$(curl -s -X POST -H 'Content-Type: application/json' -d "$login_data" "$API_BASE_URL/auth/login")
    local auth_token=$(echo "$login_response" | jq -r '.data.access_token' 2>/dev/null || echo "")
    
    if [ ! -z "$auth_token" ] && [ "$auth_token" != "null" ]; then
        log_success "User Login - Token received"
    else
        log_error "User Login - No token received"
        auth_token=""
    fi
    
    # Test 4: Get User Profile (requires auth)
    if [ ! -z "$auth_token" ]; then
        test_endpoint "GET" "/users/profile" "200" "Get User Profile" "" "$auth_token"
    fi
    
    # Test 5: Get Categories
    test_endpoint "GET" "/categories" "200" "Get Categories" "" ""
    
    # Test 6: Get Products
    test_endpoint "GET" "/products" "200" "Get Products" "" ""
    
    # Test 7: Search Products
    test_endpoint "GET" "/products/search?q=test" "200" "Search Products" "" ""
    
    # Test 8: Get Cart (requires auth)
    if [ ! -z "$auth_token" ]; then
        test_endpoint "GET" "/cart" "200" "Get Cart" "" "$auth_token"
    fi
    
    # Test 9: Get Coupons
    test_endpoint "GET" "/coupons/active" "200" "Get Active Coupons" "" ""
    
    # Test 10: Invalid endpoint
    test_endpoint "GET" "/invalid-endpoint" "404" "Invalid Endpoint (404 test)" "" ""
    
    # Test 11: Rate limiting test
    log_info "Testing rate limiting..."
    for i in {1..10}; do
        local status=$(curl -s -w '%{http_code}' -o /dev/null "$API_BASE_URL/auth/login")
        if [ "$status" = "429" ]; then
            log_success "Rate limiting is working - Got 429 status"
            break
        fi
        sleep 0.1
    done
    
    echo "=================================="
    log_info "Test Summary:"
    log_info "Tests Passed: $TESTS_PASSED"
    log_info "Tests Failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_success "All tests passed! 🎉"
        return 0
    else
        log_error "Some tests failed! ❌"
        return 1
    fi
}

# Database connectivity test
test_database() {
    log_info "Testing database connectivity..."
    
    if docker exec ecom_postgres pg_isready -U postgres -d ecommerce_db > /dev/null 2>&1; then
        log_success "Database is accessible"
    else
        log_error "Database is not accessible"
    fi
}

# Redis connectivity test
test_redis() {
    log_info "Testing Redis connectivity..."
    
    if docker exec ecom_redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis is accessible"
    else
        log_error "Redis is not accessible"
    fi
}

# Performance test
performance_test() {
    log_info "Running basic performance test..."
    
    # Test concurrent requests
    local concurrent_requests=10
    local total_requests=100
    
    log_info "Testing $total_requests requests with $concurrent_requests concurrent connections..."
    
    if command -v ab > /dev/null 2>&1; then
        ab -n $total_requests -c $concurrent_requests "$HEALTH_URL" > /tmp/ab_results.txt 2>&1
        
        local requests_per_second=$(grep "Requests per second" /tmp/ab_results.txt | awk '{print $4}')
        local time_per_request=$(grep "Time per request" /tmp/ab_results.txt | head -1 | awk '{print $4}')
        
        log_info "Performance Results:"
        log_info "  Requests per second: $requests_per_second"
        log_info "  Time per request: ${time_per_request}ms"
        
        rm -f /tmp/ab_results.txt
    else
        log_warning "Apache Bench (ab) not available, skipping performance test"
    fi
}

# Security test
security_test() {
    log_info "Running basic security tests..."
    
    # Test CORS headers
    local cors_response=$(curl -s -H "Origin: http://malicious-site.com" -I "$HEALTH_URL")
    if echo "$cors_response" | grep -q "Access-Control-Allow-Origin"; then
        log_warning "CORS headers present - verify configuration"
    else
        log_info "CORS headers not present for unauthorized origin"
    fi
    
    # Test SQL injection attempt
    test_endpoint "GET" "/products?search='; DROP TABLE users; --" "400" "SQL Injection Protection" "" ""
    
    # Test XSS attempt
    test_endpoint "GET" "/products?search=<script>alert('xss')</script>" "400" "XSS Protection" "" ""
}

# Main execution
main() {
    log_info "E-commerce System Test Suite"
    echo "============================"
    
    # Check if services are running
    if ! docker ps | grep -q ecom_api; then
        log_error "API container is not running. Please start with: docker-compose up -d"
        exit 1
    fi
    
    # Wait for services
    wait_for_service "$HEALTH_URL" "API Service" || exit 1
    
    # Run tests
    test_database
    test_redis
    run_tests
    performance_test
    security_test
    
    echo "============================"
    log_info "Test suite completed!"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        exit 0
    else
        exit 1
    fi
}

# Check dependencies
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl > /dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v jq > /dev/null 2>&1; then
        missing_deps+=("jq")
    fi
    
    if ! command -v docker > /dev/null 2>&1; then
        missing_deps+=("docker")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "Missing dependencies: ${missing_deps[*]}"
        log_info "Please install missing dependencies and try again"
        exit 1
    fi
}

# Script entry point
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    check_dependencies
    main "$@"
fi
